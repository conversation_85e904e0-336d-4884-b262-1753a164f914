function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-login-index.BAJjaSi_.js","assets/uni-icons.CkwqPbHP.js","assets/_plugin-vue_export-helper.BCo6x5W8.js","assets/uni-icons-fZgElzf2.css","assets/uni-app.es.KXR2Mdtb.js","assets/uni-popup.Ctf3w-MR.js","assets/uni-popup-Cn3s6juI.css","assets/request.BPygAub8.js","assets/index.B6QF5Ba_.js","assets/i18n.BboiN7zh.js","assets/index-Bn_3Gjko.css","assets/pages-register-index.W0z0bVn4.js","assets/index-CcYDj8W5.css","assets/pages-index-index.CnCzbXlV.js","assets/index--ojW17mm.css","assets/pages-share-index.uJr_5zth.js","assets/custom-navbar.D7fsSS_d.js","assets/custom-navbar-rFWD1v9M.css","assets/uqrcode.BN_KsmDm.js","assets/index-J3gMhE29.css","assets/pages-market-index.Bs2bCU3d.js","assets/index-ByouIIx3.css","assets/pages-mine-index.zbjwOq2U.js","assets/index-DSyR-4gL.css","assets/pages-copy-mycopy.DB5VKriP.js","assets/mycopy-sum5YGLh.css","assets/pages-leader-index.ZeLpxzl6.js","assets/index-D9DCuPFK.css","assets/pages-mine-upload-avatar.DyiqGQSV.js","assets/upload-avatar-BU1Zst8e.css","assets/pages-switchaccount-index.Cw1W2Yv-.js","assets/index-GTM8uiBU.css","assets/pages-agreement-index.8vVj5LbV.js","assets/index-DK_ZEYHG.css","assets/pages-legalsystem-index.D1mRVDTd.js","assets/index-g6lSGPRP.css","assets/pages-accountset-index.B0cXz0C9.js","assets/index-Dr6LpMGB.css","assets/pages-updatepassword-index.Dzr2YboS.js","assets/index-80ygqyyI.css","assets/pages-updatesafepwd-index.BVEID7FE.js","assets/index-DTkUWxD5.css","assets/pages-updatephone-index.C3Qxu98q.js","assets/index-o0KK84fz.css","assets/pages-noticelist-index.CapRg9On.js","assets/index-D29syXFR.css","assets/pages-noticedetail-index.DY1w80sY.js","assets/index-B7oDtZtG.css","assets/pages-transfer-index.BAHJD0AT.js","assets/index-B1IEacrF.css","assets/pages-recharge-index.--dSLA43.js","assets/index-CMTRILdP.css","assets/pages-reset-index.UvTBv7zh.js","assets/index--f_3JSd1.css","assets/pages-futures-index.BmEuO7dw.js","assets/index-ClquupGw.css","assets/pages-robot-index.JQftZOFJ.js","assets/index-CXwivZS9.css","assets/pages-kline-index.C_R3kEtq.js","assets/index-DiQIFeQp.css","assets/pages-share-record.rOY91Nkm.js","assets/record-DJ8EGO7I.css","assets/pages-asset-index.C_FFT5bJ.js","assets/index-38iKiCCL.css","assets/pages-withdraw-index.DDNKAaw-.js","assets/index-DWaH3nK0.css","assets/pages-transfer-record.DSuVYB3n.js","assets/record-Bm3hpALa.css","assets/pages-withdraw-record.Cwe-gq2-.js","assets/record-_tbNXWHs.css","assets/pages-profit-index.BRu9zYZ6.js","assets/index-DjV2h5OM.css","assets/pages-team-index.CZzO43pd.js","assets/index-BrnThz8g.css","assets/pages-account-transfer-record.BR1CDrpi.js","assets/record-_9CCwNvf.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if((n=function(e){return"/"+e}(n))in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=n,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,t)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),p=Array.isArray,h=e=>"[object Map]"===x(e),m=e=>"[object Set]"===x(e),g=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,b=e=>null!==e&&"object"==typeof e,_=e=>(b(e)||g(e))&&g(e.then)&&g(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),T=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,O=k((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),L=/\B([A-Z])/g,$=k((e=>e.replace(L,"-$1").toLowerCase())),A=k((e=>e.charAt(0).toUpperCase()+e.slice(1))),P=k((e=>e?`on${A(e)}`:"")),I=(e,t)=>!Object.is(e,t),M=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},B=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let D;const j=()=>D||(D="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?q(o):N(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||b(e))return e}const V=/;(?![^(]*\))/g,F=/:([^]+)/,H=/\/\*[^]*?\*\//g;function q(e){const t={};return e.replace(H,"").split(V).forEach((e=>{if(e){const n=e.split(F);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function z(e){let t="";if(v(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const o=z(e[n]);o&&(t+=o+" ")}else if(b(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const W=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function U(e){return!!e||""===e}const Y=e=>v(e)?e:null==e?"":p(e)||b(e)&&(e.toString===w||!g(e.toString))?JSON.stringify(e,X,2):String(e),X=(e,t)=>t&&t.__v_isRef?X(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[K(t,o)+" =>"]=n,e)),{})}:m(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>K(e)))}:y(t)?K(t):!b(t)||p(t)||S(t)?t:String(t),K=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},G=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),Z=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),J=["list-item"].map((e=>"uni-"+e));function Q(e){if(-1!==J.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==G.indexOf(t)||-1!==Z.indexOf(t)}const ee=["%","%"],te=/^([a-z-]+:)?\/\//i,ne=/^data:.*,.*/;function oe(e){return 0===e.indexOf("/")}function re(e){return oe(e)?e:"/"+e}function ie(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const se=e=>e>9?e:"0"+e;function ae({date:e=new Date,mode:t="date"}){return"time"===t?se(e.getHours())+":"+se(e.getMinutes()):e.getFullYear()+"-"+se(e.getMonth()+1)+"-"+se(e.getDate())}let le;function ce(){return le||(le=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),le)}function ue(e){return e&&(e.appContext?e.proxy:e)}function de(e){if(!e)return;let t=e.type.name;for(;t&&Q($(t));)t=(e=e.parent).type.name;return e.proxy}function fe(e){return 1===e.nodeType}function pe(e){const t=ce();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),N(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),N(t)}if(v(e))return q(e);if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?q(o):pe(o);if(r)for(const e in r)t[e]=r[e]}return t}return N(e)}function he(e){let t="";const n=ce();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(p(e))for(let o=0;o<e.length;o++){const n=he(e[o]);n&&(t+=n+" ")}else t=z(e);return t.trim()}function me(e){return O(e.substring(5))}const ge=ie((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[me(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[me(t)],o.call(this,t)}}));function ve(e){return c({},e.dataset,e.__uniDataset)}const ye=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function be(e){return{passive:e}}function _e(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:ve(e),offsetTop:n,offsetLeft:o}}function we(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function xe(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=we(e[n])}catch(o){t[n]=e[n]}})),t}const Se=/\+/g;function Te(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Se," ");let r=e.indexOf("="),i=we(r<0?e:e.slice(0,r)),s=r<0?null:we(e.slice(r+1));if(i in t){let e=t[i];p(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ce(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class ke{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Ee=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Oe=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Le=[];const $e=ie(((e,t)=>t(e))),Ae=function(){};Ae.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Pe=Ae;const Ie={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Me(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Ie?Ie[o]:o}return r}var o;return t}function Re(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=S(s)?Re(s,t,n):p(s)?s.map((e=>S(e)?Re(e,t,n):Me(o,e))):Me(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Be,De;class je{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Be,!e&&Be&&(this.index=(Be.scopes||(Be.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Be;try{return Be=this,e()}finally{Be=t}}}on(){Be=this}off(){Be=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Ne(e){return new je(e)}class Ve{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Be){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Ye();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Xe()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=ze,t=De;try{return ze=!0,De=this,this._runnings++,Fe(this),this.fn()}finally{He(this),this._runnings--,De=t,ze=e}}stop(){var e;this.active&&(Fe(this),He(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Fe(e){e._trackId++,e._depsLength=0}function He(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)qe(e.deps[t],e);e.deps.length=e._depsLength}}function qe(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let ze=!0,We=0;const Ue=[];function Ye(){Ue.push(ze),ze=!1}function Xe(){const e=Ue.pop();ze=void 0===e||e}function Ke(){We++}function Ge(){for(We--;!We&&Je.length;)Je.shift()()}function Ze(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&qe(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Je=[];function Qe(e,t,n){Ke();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Je.push(o.scheduler)))}Ge()}const et=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},tt=new WeakMap,nt=Symbol(""),ot=Symbol("");function rt(e,t,n){if(ze&&De){let t=tt.get(e);t||tt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=et((()=>t.delete(n)))),Ze(De,o)}}function it(e,t,n,o,r,i){const s=tt.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&p(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":p(e)?T(n)&&a.push(s.get("length")):(a.push(s.get(nt)),h(e)&&a.push(s.get(ot)));break;case"delete":p(e)||(a.push(s.get(nt)),h(e)&&a.push(s.get(ot)));break;case"set":h(e)&&a.push(s.get(nt))}Ke();for(const l of a)l&&Qe(l,4);Ge()}const st=n("__proto__,__v_isRef,__isVue"),at=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),lt=ct();function ct(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Zt(this);for(let t=0,r=this.length;t<r;t++)rt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Zt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Ye(),Ke();const n=Zt(this)[t].apply(this,e);return Ge(),Xe(),n}})),e}function ut(e){const t=Zt(this);return rt(t,0,e),t.hasOwnProperty(e)}class dt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Ft:Vt:r?Nt:jt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=p(e);if(!o){if(i&&f(lt,t))return Reflect.get(lt,t,n);if("hasOwnProperty"===t)return ut}const s=Reflect.get(e,t,n);return(y(t)?at.has(t):st(t))?s:(o||rt(e,0,t),r?s:rn(s)?i&&T(t)?s:s.value:b(s)?o?Wt(s):qt(s):s)}}class ft extends dt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Xt(r);if(Kt(n)||Xt(n)||(r=Zt(r),n=Zt(n)),!p(e)&&rn(r)&&!rn(n))return!t&&(r.value=n,!0)}const i=p(e)&&T(t)?Number(t)<e.length:f(e,t),s=Reflect.set(e,t,n,o);return e===Zt(o)&&(i?I(n,r)&&it(e,"set",t,n):it(e,"add",t,n)),s}deleteProperty(e,t){const n=f(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&it(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&at.has(t)||rt(e,0,t),n}ownKeys(e){return rt(e,0,p(e)?"length":nt),Reflect.ownKeys(e)}}class pt extends dt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const ht=new ft,mt=new pt,gt=new ft(!0),vt=e=>e,yt=e=>Reflect.getPrototypeOf(e);function bt(e,t,n=!1,o=!1){const r=Zt(e=e.__v_raw),i=Zt(t);n||(I(t,i)&&rt(r,0,t),rt(r,0,i));const{has:s}=yt(r),a=o?vt:n?en:Qt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function _t(e,t=!1){const n=this.__v_raw,o=Zt(n),r=Zt(e);return t||(I(e,r)&&rt(o,0,e),rt(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function wt(e,t=!1){return e=e.__v_raw,!t&&rt(Zt(e),0,nt),Reflect.get(e,"size",e)}function xt(e){e=Zt(e);const t=Zt(this);return yt(t).has.call(t,e)||(t.add(e),it(t,"add",e,e)),this}function St(e,t){t=Zt(t);const n=Zt(this),{has:o,get:r}=yt(n);let i=o.call(n,e);i||(e=Zt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?I(t,s)&&it(n,"set",e,t):it(n,"add",e,t),this}function Tt(e){const t=Zt(this),{has:n,get:o}=yt(t);let r=n.call(t,e);r||(e=Zt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&it(t,"delete",e,void 0),i}function Ct(){const e=Zt(this),t=0!==e.size,n=e.clear();return t&&it(e,"clear",void 0,void 0),n}function kt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Zt(i),a=t?vt:e?en:Qt;return!e&&rt(s,0,nt),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Et(e,t,n){return function(...o){const r=this.__v_raw,i=Zt(r),s=h(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?vt:t?en:Qt;return!t&&rt(i,0,l?ot:nt),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ot(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Lt(){const e={get(e){return bt(this,e)},get size(){return wt(this)},has:_t,add:xt,set:St,delete:Tt,clear:Ct,forEach:kt(!1,!1)},t={get(e){return bt(this,e,!1,!0)},get size(){return wt(this)},has:_t,add:xt,set:St,delete:Tt,clear:Ct,forEach:kt(!1,!0)},n={get(e){return bt(this,e,!0)},get size(){return wt(this,!0)},has(e){return _t.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:kt(!0,!1)},o={get(e){return bt(this,e,!0,!0)},get size(){return wt(this,!0)},has(e){return _t.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:kt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Et(r,!1,!1),n[r]=Et(r,!0,!1),t[r]=Et(r,!1,!0),o[r]=Et(r,!0,!0)})),[e,n,t,o]}const[$t,At,Pt,It]=Lt();function Mt(e,t){const n=t?e?It:Pt:e?At:$t;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(f(n,o)&&o in t?n:t,o,r)}const Rt={get:Mt(!1,!1)},Bt={get:Mt(!1,!0)},Dt={get:Mt(!0,!1)},jt=new WeakMap,Nt=new WeakMap,Vt=new WeakMap,Ft=new WeakMap;function Ht(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function qt(e){return Xt(e)?e:Ut(e,!1,ht,Rt,jt)}function zt(e){return Ut(e,!1,gt,Bt,Nt)}function Wt(e){return Ut(e,!0,mt,Dt,Vt)}function Ut(e,t,n,o,r){if(!b(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Ht(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Yt(e){return Xt(e)?Yt(e.__v_raw):!(!e||!e.__v_isReactive)}function Xt(e){return!(!e||!e.__v_isReadonly)}function Kt(e){return!(!e||!e.__v_isShallow)}function Gt(e){return Yt(e)||Xt(e)}function Zt(e){const t=e&&e.__v_raw;return t?Zt(t):e}function Jt(e){return Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const Qt=e=>b(e)?qt(e):e,en=e=>b(e)?Wt(e):e;class tn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ve((()=>e(this._value)),(()=>on(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Zt(this);return e._cacheable&&!e.effect.dirty||!I(e._value,e._value=e.effect.run())||on(e,4),nn(e),e.effect._dirtyLevel>=2&&on(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function nn(e){var t;ze&&De&&(e=Zt(e),Ze(De,null!=(t=e.dep)?t:e.dep=et((()=>e.dep=void 0),e instanceof tn?e:void 0)))}function on(e,t=4,n){const o=(e=Zt(e)).dep;o&&Qe(o,t)}function rn(e){return!(!e||!0!==e.__v_isRef)}function sn(e){return an(e,!1)}function an(e,t){return rn(e)?e:new ln(e,t)}class ln{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Zt(e),this._value=t?e:Qt(e)}get value(){return nn(this),this._value}set value(e){const t=this.__v_isShallow||Kt(e)||Xt(e);e=t?e:Zt(e),I(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Qt(e),on(this,4))}}function cn(e){return rn(e)?e.value:e}const un={get:(e,t,n)=>cn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return rn(r)&&!rn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function dn(e){return Yt(e)?e:new Proxy(e,un)}function fn(e,t,n,o){try{return o?e(...o):e()}catch(r){hn(r,t,n)}}function pn(e,t,n,o){if(g(e)){const r=fn(e,t,n,o);return r&&_(r)&&r.catch((e=>{hn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(pn(e[i],t,n,o));return r}function hn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void fn(s,null,10,[e,r,i])}mn(e,n,r,o)}function mn(e,t,n,o=!0){console.error(e)}let gn=!1,vn=!1;const yn=[];let bn=0;const _n=[];let wn=null,xn=0;const Sn=Promise.resolve();let Tn=null;function Cn(e){const t=Tn||Sn;return e?t.then(this?e.bind(this):e):t}function kn(e){yn.length&&yn.includes(e,gn&&e.allowRecurse?bn+1:bn)||(null==e.id?yn.push(e):yn.splice(function(e){let t=bn+1,n=yn.length;for(;t<n;){const o=t+n>>>1,r=yn[o],i=$n(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),En())}function En(){gn||vn||(vn=!0,Tn=Sn.then(Pn))}function On(e,t,n=(gn?bn+1:0)){for(;n<yn.length;n++){const t=yn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;yn.splice(n,1),n--,t()}}}function Ln(e){if(_n.length){const e=[...new Set(_n)].sort(((e,t)=>$n(e)-$n(t)));if(_n.length=0,wn)return void wn.push(...e);for(wn=e,xn=0;xn<wn.length;xn++)wn[xn]();wn=null,xn=0}}const $n=e=>null==e.id?1/0:e.id,An=(e,t)=>{const n=$n(e)-$n(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Pn(e){vn=!1,gn=!0,yn.sort(An);try{for(bn=0;bn<yn.length;bn++){const e=yn[bn];e&&!1!==e.active&&fn(e,null,14)}}finally{bn=0,yn.length=0,Ln(),gn=!1,Tn=null,(yn.length||_n.length)&&Pn()}}function In(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(B))}let l,c=r[l=P(t)]||r[l=P(O(t))];!c&&s&&(c=r[l=P($(t))]),c&&pn(c,e,6,Mn(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,pn(u,e,6,Mn(e,u,i))}}function Mn(e,t,n){if(1!==n.length)return n;if(g(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&f(o,"type")&&f(o,"timeStamp")&&f(o,"target")&&f(o,"currentTarget")&&f(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Rn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!g(e)){const o=e=>{const n=Rn(e,t,!0);n&&(a=!0,c(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(p(i)?i.forEach((e=>s[e]=null)):c(s,i),b(e)&&o.set(e,s),s):(b(e)&&o.set(e,null),null)}function Bn(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,$(t))||f(e,t))}let Dn=null,jn=null;function Nn(e){const t=Dn;return Dn=e,jn=e&&e.type.__scopeId||null,t}function Vn(e,t=Dn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Yr(-1);const r=Nn(t);let i;try{i=e(...n)}finally{Nn(r),o._d&&Yr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Fn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:u,render:d,renderCache:f,data:p,setupState:h,ctx:m,inheritAttrs:g}=e;let v,y;const b=Nn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=ai(d.call(t,e,f,i,h,p,m)),y=c}else{const e=t;0,v=ai(e.length>1?e(i,{attrs:c,slots:a,emit:u}):e(i,null)),y=t.props?c:Hn(c)}}catch(w){qr.length=0,hn(w,e,1),v=oi(Fr)}let _=v;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(s&&e.some(l)&&(y=qn(y,s)),_=ri(_,y))}return n.dirs&&(_=ri(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,Nn(b),v}const Hn=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},qn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function zn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Bn(n,i))return!0}return!1}function Wn(e,t){return Xn("components",e,!0,t)||e}const Un=Symbol.for("v-ndc");function Yn(e){return v(e)?Xn("components",e,!1)||e:e||Un}function Xn(e,t,n=!0,o=!1){const r=Dn||hi;if(r){const n=r.type;if("components"===e){const e=Ci(n,!1);if(e&&(e===t||e===O(t)||e===A(O(t))))return n}const i=Kn(r[e]||n[e],t)||Kn(r.appContext[e],t);return!i&&o?n:i}}function Kn(e,t){return e&&(e[t]||e[O(t)]||e[A(O(t))])}const Gn=e=>e.__isSuspense;const Zn=Symbol.for("v-scx");function Jn(e,t){return to(e,null,t)}const Qn={};function eo(e,t,n){return to(e,t,n)}function to(e,t,{immediate:n,deep:r,flush:s,once:a,onTrack:l,onTrigger:c}=o){if(t&&a){const e=t;t=(...t)=>{e(...t),k()}}const d=hi,f=e=>!0===r?e:ro(e,!1===r?1:void 0);let h,m,v=!1,y=!1;if(rn(e)?(h=()=>e.value,v=Kt(e)):Yt(e)?(h=()=>f(e),v=!0):p(e)?(y=!0,v=e.some((e=>Yt(e)||Kt(e))),h=()=>e.map((e=>rn(e)?e.value:Yt(e)?f(e):g(e)?fn(e,d,2):void 0))):h=g(e)?t?()=>fn(e,d,2):()=>(m&&m(),pn(e,d,3,[_])):i,t&&r){const e=h;h=()=>ro(e())}let b,_=e=>{m=T.onStop=()=>{fn(e,d,4),m=T.onStop=void 0}};if(wi){if(_=i,t?n&&pn(t,d,3,[h(),y?[]:void 0,_]):h(),"sync"!==s)return i;{const e=br(Zn);b=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(Qn):Qn;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(r||v||(y?e.some(((e,t)=>I(e,w[t]))):I(e,w)))&&(m&&m(),pn(t,d,3,[e,w===Qn?void 0:y&&w[0]===Qn?[]:w,_]),w=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===s?S=x:"post"===s?S=()=>Ir(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),S=()=>kn(x));const T=new Ve(h,i,S),C=Be,k=()=>{T.stop(),C&&u(C.effects,T)};return t?n?x():w=T.run():"post"===s?Ir(T.run.bind(T),d&&d.suspense):T.run(),b&&b.push(k),k}function no(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?oo(o,e):()=>o[e]:e.bind(o,o);let i;g(t)?i=t:(i=t.handler,n=t);const s=yi(this),a=to(r,i.bind(o),n);return s(),a}function oo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function ro(e,t,n=0,o){if(!b(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),rn(e))ro(e.value,t,n,o);else if(p(e))for(let r=0;r<e.length;r++)ro(e[r],t,n,o);else if(m(e)||h(e))e.forEach((e=>{ro(e,t,n,o)}));else if(S(e))for(const r in e)ro(e[r],t,n,o);return e}function io(e,t){if(null===Dn)return e;const n=Ti(Dn)||Dn.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,s,a,l=o]=t[i];e&&(g(e)&&(e={mounted:e,updated:e}),e.deep&&ro(s),r.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function so(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Ye(),pn(l,n,8,[e.el,a,e,t]),Xe())}}const ao=Symbol("_leaveCb"),lo=Symbol("_enterCb");const co=[Function,Array],uo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:co,onEnter:co,onAfterEnter:co,onEnterCancelled:co,onBeforeLeave:co,onLeave:co,onAfterLeave:co,onLeaveCancelled:co,onBeforeAppear:co,onAppear:co,onAfterAppear:co,onAppearCancelled:co},fo={name:"BaseTransition",props:uo,setup(e,{slots:t}){const n=mi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return No((()=>{e.isMounted=!0})),Ho((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&yo(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Fr){i=e;break}const s=Zt(e),{mode:a}=s;if(o.isLeaving)return mo(i);const l=go(i);if(!l)return mo(i);const c=ho(l,s,o,n);vo(l,c);const u=n.subTree,d=u&&go(u);if(d&&d.type!==Fr&&!Jr(l,d)){const e=ho(d,s,o,n);if(vo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},mo(i);"in-out"===a&&l.type!==Fr&&(e.delayLeave=(e,t,n)=>{po(o,d)[String(d.key)]=d,e[ao]=()=>{t(),e[ao]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function po(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ho(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:h,onLeaveCancelled:m,onBeforeAppear:g,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=po(n,e),x=(e,t)=>{e&&pn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),p(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=g||a}t[ao]&&t[ao](!0);const i=w[_];i&&Jr(e,i)&&i.el[ao]&&i.el[ao](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[lo]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[lo]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[lo]&&t[lo](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[ao]=n=>{i||(i=!0,o(),x(n?m:h,[t]),t[ao]=void 0,w[r]===e&&delete w[r])};w[r]=e,f?S(f,[t,s]):s()},clone:e=>ho(e,t,n,o)};return T}function mo(e){if(So(e))return(e=ri(e)).children=null,e}function go(e){return So(e)?e.children?e.children[0]:void 0:e}function vo(e,t){6&e.shapeFlag&&e.component?vo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function yo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Nr?(128&s.patchFlag&&r++,o=o.concat(yo(s.children,t,a))):(t||s.type!==Fr)&&o.push(null!=a?ri(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function bo(e,t){return g(e)?(()=>c({name:e.name},t,{setup:e}))():e}const _o=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function wo(e){g(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return bo({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=hi;if(l)return()=>xo(l,e);const t=t=>{c=null,hn(t,e,13,!o)};if(s&&e.suspense||wi)return d().then((t=>()=>xo(t,e))).catch((e=>(t(e),()=>o?oi(o,{error:e}):null)));const a=sn(!1),u=sn(),f=sn(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&So(e.parent.vnode)&&(e.parent.effect.dirty=!0,kn(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?xo(l,e):u.value&&o?oi(o,{error:u.value}):n&&!f.value?oi(n):void 0}})}function xo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=oi(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const So=e=>e.type.__isKeepAlive;class To{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Co={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=mi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new To(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Jr(t,i)||"key"===e.matchBy&&t.key!==i.key?(Po(o=t),u(o,n,a,!0)):i&&Po(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function p(t){r.forEach(((n,o)=>{const i=Mo(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,M(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Ir((()=>{i.isDeactivated=!1,i.a&&M(i.a);const t=e.props&&e.props.onVnodeMounted;t&&di(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Ro(t.bda),c(e,f,null,1,a),Ir((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&M(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&di(n,t.parent,e),t.isDeactivated=!0}),a)},eo((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&p((t=>Eo(e,t))),t&&p((e=>!Eo(t,e)))}),{flush:"post",deep:!0});let h=null;const m=()=>{null!=h&&r.set(h,Io(n.subTree))};return No(m),Fo(m),Ho((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=Io(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&M(l.component.bda),Po(l);const e=l.component.da;e&&Ir(e,a)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Zr(o)||!(4&o.shapeFlag)&&!Gn(o.type))return i=null,o;let s=Io(o);const a=s.type,l=Mo(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Eo(c,l))||u&&l&&Eo(u,l))return i=s,o;const d=null==s.key?a:s.key,f=r.get(d);return s.el&&(s=ri(s),Gn(o.type)&&(o.ssContent=s)),h=d,f&&(s.el=f.el,s.component=f.component,s.transition&&vo(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Gn(o.type)?o:s}}},ko=Co;function Eo(e,t){return p(e)?e.some((e=>Eo(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function Oo(e,t){$o(e,"a",t)}function Lo(e,t){$o(e,"da",t)}function $o(e,t,n=hi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Bo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)So(e.parent.vnode)&&Ao(o,t,n,e),e=e.parent}}function Ao(e,t,n,o){const r=Bo(t,e,o,!0);qo((()=>{u(o[t],r)}),n)}function Po(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Io(e){return Gn(e.type)?e.ssContent:e}function Mo(e,t){if("name"===t){const t=e.type;return Ci(_o(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Ro(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Bo(e,t,n=hi,o=!1){if(n){if(r=e,Ee.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;pn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Ye();const r=yi(n),i=pn(t,n,e,o);return r(),Xe(),i});return o?i.unshift(s):i.push(s),s}var r}const Do=e=>(t,n=hi)=>(!wi||"sp"===e)&&Bo(e,((...e)=>t(...e)),n),jo=Do("bm"),No=Do("m"),Vo=Do("bu"),Fo=Do("u"),Ho=Do("bum"),qo=Do("um"),zo=Do("sp"),Wo=Do("rtg"),Uo=Do("rtc");function Yo(e,t=hi){Bo("ec",e,t)}function Xo(e,t,n,o){let r;const i=n&&n[o];if(p(e)||v(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(b(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Ko(e,t,n={},o,r){if(Dn.isCE||Dn.parent&&_o(Dn.parent)&&Dn.parent.isCE)return"default"!==t&&(n.name=t),oi("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Wr();const s=i&&Go(i(n)),a=Gr(Nr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Go(e){return e.some((e=>!Zr(e)||e.type!==Fr&&!(e.type===Nr&&!Go(e.children))))?e:null}const Zo=e=>{if(!e)return null;if(_i(e)){return Ti(e)||e.proxy}return Zo(e.parent)},Jo=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Zo(e.parent),$root:e=>Zo(e.root),$emit:e=>e.emit,$options:e=>sr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,kn(e.update)})(e)),$nextTick:e=>e.n||(e.n=Cn.bind(e.proxy)),$watch:e=>no.bind(e)}),Qo=(e,t)=>e!==o&&!e.__isScriptSetup&&f(e,t),er={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(Qo(r,t))return a[t]=1,r[t];if(i!==o&&f(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&f(u,t))return a[t]=3,s[t];if(n!==o&&f(n,t))return a[t]=4,n[t];nr&&(a[t]=0)}}const d=Jo[t];let p,h;return d?("$attrs"===t&&rt(e,0,t),d(e)):(p=l.__cssModules)&&(p=p[t])?p:n!==o&&f(n,t)?(a[t]=4,n[t]):(h=c.config.globalProperties,f(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return Qo(i,t)?(i[t]=n,!0):r!==o&&f(r,t)?(r[t]=n,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},a){let l;return!!n[a]||e!==o&&f(e,a)||Qo(t,a)||(l=s[0])&&f(l,a)||f(r,a)||f(Jo,a)||f(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function tr(e){return p(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let nr=!0;function or(e){const t=sr(e),n=e.proxy,o=e.ctx;nr=!1,t.beforeCreate&&rr(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:h,beforeUpdate:m,updated:v,activated:y,deactivated:_,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:C,renderTracked:k,renderTriggered:E,errorCaptured:O,serverPrefetch:L,expose:$,inheritAttrs:A,components:P,directives:I,filters:M}=t;if(u&&function(e,t,n=i){p(e)&&(e=ur(e));for(const o in e){const n=e[o];let r;r=b(n)?"default"in n?br(n.from||o,n.default,!0):br(n.from||o):br(n),rn(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),a)for(const i in a){const e=a[i];g(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);b(t)&&(e.data=qt(t))}if(nr=!0,s)for(const p in s){const e=s[p],t=g(e)?e.bind(n,n):g(e.get)?e.get.bind(n,n):i,r=!g(e)&&g(e.set)?e.set.bind(n):i,a=ki({get:t,set:r});Object.defineProperty(o,p,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)ir(l[i],o,n,i);if(c){const e=g(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{yr(t,e[t])}))}function R(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&rr(d,e,"c"),R(jo,f),R(No,h),R(Vo,m),R(Fo,v),R(Oo,y),R(Lo,_),R(Yo,O),R(Uo,k),R(Wo,E),R(Ho,x),R(qo,T),R(zo,L),p($))if($.length){const t=e.exposed||(e.exposed={});$.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===i&&(e.render=C),null!=A&&(e.inheritAttrs=A),P&&(e.components=P),I&&(e.directives=I);const B=e.appContext.config.globalProperties.$applyOptions;B&&B(t,e,n)}function rr(e,t,n){pn(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function ir(e,t,n,o){const r=o.includes(".")?oo(n,o):()=>n[o];if(v(e)){const n=t[e];g(n)&&eo(r,n)}else if(g(e))eo(r,e.bind(n));else if(b(e))if(p(e))e.forEach((e=>ir(e,t,n,o)));else{const o=g(e.handler)?e.handler.bind(n):t[e.handler];g(o)&&eo(r,o,e)}}function sr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>ar(l,e,s,!0))),ar(l,t,s)):l=t,b(t)&&i.set(t,l),l}function ar(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&ar(e,i,n,!0),r&&r.forEach((t=>ar(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=lr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const lr={data:cr,props:pr,emits:pr,methods:fr,computed:fr,beforeCreate:dr,created:dr,beforeMount:dr,mounted:dr,beforeUpdate:dr,updated:dr,beforeDestroy:dr,beforeUnmount:dr,destroyed:dr,unmounted:dr,activated:dr,deactivated:dr,errorCaptured:dr,serverPrefetch:dr,components:fr,directives:fr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=dr(e[o],t[o]);return n},provide:cr,inject:function(e,t){return fr(ur(e),ur(t))}};function cr(e,t){return t?e?function(){return c(g(e)?e.call(this,this):e,g(t)?t.call(this,this):t)}:t:e}function ur(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function dr(e,t){return e?[...new Set([].concat(e,t))]:t}function fr(e,t){return e?c(Object.create(null),e,t):t}function pr(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:c(Object.create(null),tr(e),tr(null!=t?t:{})):t}function hr(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let mr=0;function gr(e,t){return function(n,o=null){g(n)||(n=c({},n)),null==o||b(o)||(o=null);const r=hr(),i=new WeakSet;let s=!1;const a=r.app={_uid:mr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Oi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&g(e.install)?(i.add(e),e.install(a,...t)):g(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=oi(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,Ti(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=vr;vr=a;try{return e()}finally{vr=t}}};return a}}let vr=null;function yr(e,t){if(hi){let n=hi.provides;const o=hi.parent&&hi.parent.provides;o===n&&(n=hi.provides=Object.create(o)),n[e]=t,"app"===hi.type.mpType&&hi.appContext.app.provide(e,t)}else;}function br(e,t,n=!1){const o=hi||Dn;if(o||vr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:vr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&g(t)?t.call(o&&o.proxy):t}}function _r(e,t,n,r){const[i,s]=e.propsOptions;let a,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;i&&f(i,u=O(o))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:Bn(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(s){const t=Zt(n),r=a||o;for(let o=0;o<s.length;o++){const a=s[o];n[a]=wr(i,t,a,r[a],e,!f(r,a))}}return l}function wr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=f(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&g(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=yi(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==$(n)||(o=!0))}return o}function xr(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const a=e.props,l={},u=[];let d=!1;if(!g(e)){const o=e=>{d=!0;const[n,o]=xr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!d)return b(e)&&i.set(e,r),r;if(p(a))for(let r=0;r<a.length;r++){const e=O(a[r]);Sr(e)&&(l[e]=o)}else if(a)for(const o in a){const e=O(o);if(Sr(e)){const t=a[o],n=l[e]=p(t)||g(t)?{type:t}:c({},t);if(n){const t=kr(Boolean,n.type),o=kr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||f(n,"default"))&&u.push(e)}}}const h=[l,u];return b(e)&&i.set(e,h),h}function Sr(e){return"$"!==e[0]&&!C(e)}function Tr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Cr(e,t){return Tr(e)===Tr(t)}function kr(e,t){return p(t)?t.findIndex((t=>Cr(t,e))):g(t)&&Cr(t,e)?0:-1}const Er=e=>"_"===e[0]||"$stable"===e,Or=e=>p(e)?e.map(ai):[ai(e)],Lr=(e,t,n)=>{if(t._n)return t;const o=Vn(((...e)=>Or(t(...e))),n);return o._c=!1,o},$r=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Er(r))continue;const n=e[r];if(g(n))t[r]=Lr(0,n,o);else if(null!=n){const e=Or(n);t[r]=()=>e}}},Ar=(e,t)=>{const n=Or(t);e.slots.default=()=>n};function Pr(e,t,n,r,i=!1){if(p(e))return void e.forEach(((e,o)=>Pr(e,t&&(p(t)?t[o]:t),n,r,i)));if(_o(r)&&!i)return;const s=4&r.shapeFlag?Ti(r.component)||r.component.proxy:r.el,a=i?null:s,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,m=l.setupState;if(null!=d&&d!==c&&(v(d)?(h[d]=null,f(m,d)&&(m[d]=null)):rn(d)&&(d.value=null)),g(c))fn(c,l,12,[a,h]);else{const t=v(c),o=rn(c);if(t||o){const r=()=>{if(e.f){const n=t?f(m,c)?m[c]:h[c]:c.value;i?p(n)&&u(n,s):p(n)?n.includes(s)||n.push(s):t?(h[c]=[s],f(m,c)&&(m[c]=h[c])):(c.value=[s],e.k&&(h[e.k]=c.value))}else t?(h[c]=a,f(m,c)&&(m[c]=a)):o&&(c.value=a,e.k&&(h[e.k]=a))};a?(r.id=-1,Ir(r,n)):r()}}}const Ir=function(e,t){var n;t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):(p(n=e)?_n.push(...n):wn&&wn.includes(n,n.allowRecurse?xn+1:xn)||_n.push(n),En())};function Mr(e){return function(e,t){j().__VUE__=!0;const{insert:n,remove:s,patchProp:a,forcePatchProp:l,createElement:u,createText:d,createComment:p,setText:h,setElementText:m,parentNode:g,nextSibling:v,setScopeId:y=i,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Jr(e,t)&&(o=te(e),G(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Vr:x(e,t,n,o);break;case Fr:S(e,t,n,o);break;case Hr:null==e&&T(t,n,o,s);break;case Nr:V(e,t,n,o,r,i,s,a,l);break;default:1&d?L(e,t,n,o,r,i,s,a,l):6&d?F(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Pr(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=p(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),s(e),e=n;s(t)},L=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?A(t,n,o,r,i,s,a,l):B(e,t,r,i,s,a,l)},A=(e,t,o,r,i,s,l,c)=>{let d,f;const{props:p,shapeFlag:h,transition:g,dirs:v}=e;if(d=e.el=u(e.type,s,p&&p.is,p),8&h?m(d,e.children):16&h&&I(e.children,d,null,r,i,Rr(e,s),l,c),v&&so(e,null,r,"created"),P(d,e,e.scopeId,l,r),p){for(const t in p)"value"===t||C(t)||a(d,t,null,p[t],s,e.children,r,i,ee);"value"in p&&a(d,"value",null,p.value,s),(f=p.onVnodeBeforeMount)&&di(f,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&so(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,g);y&&g.beforeEnter(d),n(d,t,o),((f=p&&p.onVnodeMounted)||y||v)&&Ir((()=>{f&&di(f,r,e),y&&g.enter(d),v&&so(e,null,r,"mounted")}),i)},P=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;P(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},I=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?li(e[c]):ai(e[c]);w(null,l,t,n,o,r,i,s,a)}},B=(e,t,n,r,i,s,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:p}=t;d|=16&e.patchFlag;const h=e.props||o,g=t.props||o;let v;if(n&&Br(n,!1),(v=g.onVnodeBeforeUpdate)&&di(v,n,t,e),p&&so(t,e,n,"beforeUpdate"),n&&Br(n,!0),f?D(e.dynamicChildren,f,u,n,r,Rr(t,i),s):c||U(e,t,u,null,n,r,Rr(t,i),s,!1),d>0){if(16&d)N(u,t,h,g,n,r,i);else if(2&d&&h.class!==g.class&&a(u,"class",null,g.class,i),4&d&&a(u,"style",h.style,g.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const s=o[t],c=h[s],d=g[s];(d!==c||"value"===s||l&&l(u,s))&&a(u,s,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&m(u,t.children)}else c||null!=f||N(u,t,h,g,n,r,i);((v=g.onVnodeUpdated)||p)&&Ir((()=>{v&&di(v,n,t,e),p&&so(t,e,n,"updated")}),r)},D=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Nr||!Jr(l,c)||70&l.shapeFlag)?g(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},N=(e,t,n,r,i,s,c)=>{if(n!==r){if(n!==o)for(const o in n)C(o)||o in r||a(e,o,n[o],null,c,t.children,i,s,ee);for(const o in r){if(C(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&a(e,o,d,u,c,t.children,i,s,ee)}"value"in r&&a(e,"value",n.value,r.value,c)}},V=(e,t,o,r,i,s,a,l,c)=>{const u=t.el=e?e.el:d(""),f=t.anchor=e?e.anchor:d("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:m}=t;m&&(l=l?l.concat(m):m),null==e?(n(u,o,r),n(f,o,r),I(t.children||[],o,f,i,s,a,l,c)):p>0&&64&p&&h&&e.dynamicChildren?(D(e.dynamicChildren,h,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Dr(e,t,!0)):U(e,t,o,f,i,s,a,l,c)},F=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):H(t,n,o,r,i,s,l):q(e,t,l)},H=(e,t,n,r,i,s,a)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||fi,s={uid:pi++,vnode:e,type:r,parent:t,appContext:i,get renderer(){return"app"===r.mpType?"app":this.$pageInstance&&this.$pageInstance==s?"page":"component"},root:null,next:null,subTree:null,effect:null,update:null,scope:new je(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xr(r,i),emitsOptions:Rn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=In.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,r,i);if(So(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&vi(t);const{props:n,children:o}=e.vnode,r=_i(e);(function(e,t,n,o=!1){const r={},i={};R(i,Qr,1),e.propsDefaults=Object.create(null),_r(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:zt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Zt(t),R(t,"_",n)):$r(t,e.slots={})}else e.slots={},t&&Ar(e,t);R(e.slots,Qr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Jt(new Proxy(e.ctx,er));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(rt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=yi(e);Ye();const i=fn(o,e,0,[e.props,n]);if(Xe(),r(),_(i)){if(i.then(bi,bi),t)return i.then((n=>{xi(e,n,t)})).catch((t=>{hn(t,e,0)}));e.asyncDep=i}else xi(e,i,t)}else Si(e,t)}(e,t):void 0;t&&vi(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,z),!e.el){const e=l.subTree=oi(Fr);S(null,e,t,n)}}else z(l,e,t,n,i,s,a)},q=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||zn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?zn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Bn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void W(o,t,n);o.next=t,function(e){const t=yn.indexOf(e);t>bn&&yn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,r,s,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=jr(e);if(n)return t&&(t.el=c.el,W(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Br(e,!1),t?(t.el=c.el,W(e,t,a)):t=c,n&&M(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&di(u,i,t,c),Br(e,!0);const f=Fn(e),p=e.subTree;e.subTree=f,w(p,f,g(p.el),te(p),e,r,s),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&Ir(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Ir((()=>di(u,i,t,c)),r)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=_o(t);if(Br(e,!1),c&&M(c),!f&&(i=l&&l.onVnodeBeforeMount)&&di(i,d,t),Br(e,!0),a&&se){const n=()=>{e.subTree=Fn(e),se(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Fn(e);w(null,i,n,o,e,r,s),t.el=i.el}if(u&&Ir(u,r),!f&&(i=l&&l.onVnodeMounted)){const e=t;Ir((()=>di(i,d,e)),r)}(256&t.shapeFlag||d&&_o(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&Ro(e.ba),e.a&&Ir(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new Ve(l,i,(()=>kn(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Br(e,!0),u()},W=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Zt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;_r(e,t,r,i)&&(c=!0);for(const i in a)t&&(f(t,i)||(o=$(i))!==i&&f(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=wr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&f(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Bn(e.emitsOptions,s))continue;const u=t[s];if(l)if(f(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=O(s);r[t]=wr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&it(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let s=!0,a=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?s=!1:(c(i,t),n||1!==e||delete i._):(s=!t.$stable,$r(t,i)),a=t}else t&&(Ar(e,t),a={default:1});if(s)for(const o in i)Er(o)||null!=a[o]||delete i[o]})(e,t.children,n),Ye(),On(e),Xe()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void X(c,d,n,o,r,i,s,a,l);if(256&f)return void Y(c,d,n,o,r,i,s,a,l)}8&p?(16&u&&ee(c,r,i),d!==c&&m(n,d)):16&u?16&p?X(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&m(n,""),16&p&&I(d,n,o,r,i,s,a,l))},Y=(e,t,n,o,i,s,a,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,f=Math.min(u,d);let p;for(p=0;p<f;p++){const o=t[p]=c?li(t[p]):ai(t[p]);w(e[p],o,n,null,i,s,a,l,c)}u>d?ee(e,i,s,!0,!1,f):I(t,n,o,i,s,a,l,c,f)},X=(e,t,n,o,i,s,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,p=d-1;for(;u<=f&&u<=p;){const o=e[u],r=t[u]=c?li(t[u]):ai(t[u]);if(!Jr(o,r))break;w(o,r,n,null,i,s,a,l,c),u++}for(;u<=f&&u<=p;){const o=e[f],r=t[p]=c?li(t[p]):ai(t[p]);if(!Jr(o,r))break;w(o,r,n,null,i,s,a,l,c),f--,p--}if(u>f){if(u<=p){const e=p+1,r=e<d?t[e].el:o;for(;u<=p;)w(null,t[u]=c?li(t[u]):ai(t[u]),n,r,i,s,a,l,c),u++}}else if(u>p)for(;u<=f;)G(e[u],i,s,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=p;u++){const e=t[u]=c?li(t[u]):ai(t[u]);null!=e.key&&g.set(e.key,u)}let v,y=0;const b=p-m+1;let _=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=h;u<=f;u++){const o=e[u];if(y>=b){G(o,i,s,!0);continue}let r;if(null!=o.key)r=g.get(o.key);else for(v=m;v<=p;v++)if(0===S[v-m]&&Jr(o,t[v])){r=v;break}void 0===r?G(o,i,s,!0):(S[r-m]=u+1,r>=x?x=r:_=!0,w(o,t[r],n,null,i,s,a,l,c),y++)}const T=_?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):r;for(v=T.length-1,u=b-1;u>=0;u--){const e=m+u,r=t[e],f=e+1<d?t[e+1].el:o;0===S[u]?w(null,r,n,f,i,s,a,l,c):_&&(v<0||u!==T[v]?K(r,n,f,2):v--)}}},K=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void K(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Nr){n(s,t,o);for(let e=0;e<c.length;e++)K(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Hr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Ir((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},G=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&Pr(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!_o(e);let m;if(h&&(m=s&&s.onVnodeBeforeUnmount)&&di(m,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&so(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Nr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Nr&&384&d||!r&&16&u)&&ee(l,t,n),o&&Z(e)}(h&&(m=s&&s.onVnodeUnmounted)||p)&&Ir((()=>{m&&di(m,t,e),p&&so(e,null,t,"unmounted")}),n)},Z=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Nr)return void J(n,o);if(t===Hr)return void E(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,s=()=>t(n,i);o?o(e.el,i,s):s()}else i()},J=(e,t)=>{let n;for(;e!==t;)n=v(e),s(e),e=n;s(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&M(o),r.stop(),i&&(i.active=!1,G(s,e,t,n)),a&&Ir(a,t),Ir((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)G(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,On(),Ln(),ne=!1),t._vnode=e},re={p:w,um:G,m:K,r:Z,mt:H,mc:I,pc:U,pbc:D,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:gr(oe,ie)}}(e)}function Rr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Br({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Dr(e,t,n=!1){const o=e.children,r=t.children;if(p(o)&&p(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=li(r[i]),t.el=e.el),n||Dr(e,t)),t.type===Vr&&(t.el=e.el)}}function jr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:jr(t)}const Nr=Symbol.for("v-fgt"),Vr=Symbol.for("v-txt"),Fr=Symbol.for("v-cmt"),Hr=Symbol.for("v-stc"),qr=[];let zr=null;function Wr(e=!1){qr.push(zr=e?null:[])}let Ur=1;function Yr(e){Ur+=e}function Xr(e){return e.dynamicChildren=Ur>0?zr||r:null,qr.pop(),zr=qr[qr.length-1]||null,Ur>0&&zr&&zr.push(e),e}function Kr(e,t,n,o,r,i){return Xr(ni(e,t,n,o,r,i,!0))}function Gr(e,t,n,o,r){return Xr(oi(e,t,n,o,r,!0))}function Zr(e){return!!e&&!0===e.__v_isVNode}function Jr(e,t){return e.type===t.type&&e.key===t.key}const Qr="__vInternal",ei=({key:e})=>null!=e?e:null,ti=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||rn(e)||g(e)?{i:Dn,r:e,k:t,f:!!n}:e:null);function ni(e,t=null,n=null,o=0,r=null,i=(e===Nr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ei(t),ref:t&&ti(t),scopeId:jn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Dn};return a?(ci(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Ur>0&&!s&&zr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&zr.push(l),l}const oi=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Un||(e=Fr);if(Zr(e)){const o=ri(e,t,!0);return n&&ci(o,n),Ur>0&&!i&&zr&&(6&o.shapeFlag?zr[zr.indexOf(e)]=o:zr.push(o)),o.patchFlag|=-2,o}s=e,g(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Gt(e)||Qr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=he(e)),b(n)&&(Gt(n)&&!p(n)&&(n=c({},n)),t.style=pe(n))}const a=v(e)?1:Gn(e)?128:(e=>e.__isTeleport)(e)?64:b(e)?4:g(e)?2:0;return ni(e,t,n,o,r,a,i,!0)};function ri(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?ui(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&ei(a),ref:t&&t.ref?n&&r?p(r)?r.concat(ti(t)):[r,ti(t)]:ti(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Nr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ri(e.ssContent),ssFallback:e.ssFallback&&ri(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ii(e=" ",t=0){return oi(Vr,null,e,t)}function si(e="",t=!1){return t?(Wr(),Gr(Fr,null,e)):oi(Fr,null,e)}function ai(e){return null==e||"boolean"==typeof e?oi(Fr):p(e)?oi(Nr,null,e.slice()):"object"==typeof e?li(e):oi(Vr,null,String(e))}function li(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ri(e)}function ci(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),ci(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Qr in t?3===o&&Dn&&(1===Dn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Dn}}else g(t)?(t={default:t,_ctx:Dn},n=32):(t=String(t),64&o?(n=16,t=[ii(t)]):n=8);e.children=t,e.shapeFlag|=n}function ui(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=he([t.class,o.class]));else if("style"===e)t.style=pe([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||p(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function di(e,t,n,o=null){pn(e,t,7,[n,o])}const fi=hr();let pi=0;let hi=null;const mi=()=>hi||Dn;let gi,vi;{const e=j(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};gi=t("__VUE_INSTANCE_SETTERS__",(e=>hi=e)),vi=t("__VUE_SSR_SETTERS__",(e=>wi=e))}const yi=e=>{const t=hi;return gi(e),e.scope.on(),()=>{e.scope.off(),gi(t)}},bi=()=>{hi&&hi.scope.off(),gi(null)};function _i(e){return 4&e.vnode.shapeFlag}let wi=!1;function xi(e,t,n){g(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:b(t)&&(e.setupState=dn(t)),Si(e,n)}function Si(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=yi(e);Ye();try{or(e)}finally{Xe(),t()}}}function Ti(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(dn(Jt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Jo?Jo[n](e):void 0,has:(e,t)=>t in e||t in Jo}))}function Ci(e,t=!0){return g(e)?e.displayName||e.name:e.name||t&&e.__name}const ki=(e,t)=>{const n=function(e,t,n=!1){let o,r;const s=g(e);return s?(o=e,r=i):(o=e.get,r=e.set),new tn(o,r,s||!r,n)}(e,0,wi);return n};function Ei(e,t,n){const o=arguments.length;return 2===o?b(t)&&!p(t)?Zr(t)?oi(e,null,[t]):oi(e,t):oi(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Zr(n)&&(n=[n]),oi(e,t,n))}const Oi="3.4.21",Li="undefined"!=typeof document?document:null,$i=Li&&Li.createElement("template"),Ai={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Li.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Li.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Li.createElement(e,{is:n}):Li.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Li.createTextNode(e),createComment:e=>Li.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Li.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{$i.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=$i.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Pi="transition",Ii=Symbol("_vtc"),Mi=(e,{slots:t})=>Ei(fo,function(e){const t={};for(const c in e)c in Ri||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:d=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(b(e))return[ji(e.enter),ji(e.leave)];{const t=ji(e);return[t,t]}}(r),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:C=_,onAppearCancelled:k=w}=t,E=(e,t,n)=>{Vi(e,t?d:a),Vi(e,t?u:s),n&&n()},O=(e,t)=>{e._isLeaving=!1,Vi(e,f),Vi(e,h),Vi(e,p),t&&t()},L=e=>(t,n)=>{const r=e?C:_,s=()=>E(t,e,n);Bi(r,[t,s]),Fi((()=>{Vi(t,e?l:i),Ni(t,e?d:a),Di(r)||qi(t,o,g,s)}))};return c(t,{onBeforeEnter(e){Bi(y,[e]),Ni(e,i),Ni(e,s)},onBeforeAppear(e){Bi(T,[e]),Ni(e,l),Ni(e,u)},onEnter:L(!1),onAppear:L(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);Ni(e,f),document.body.offsetHeight,Ni(e,p),Fi((()=>{e._isLeaving&&(Vi(e,f),Ni(e,h),Di(x)||qi(e,o,v,n))})),Bi(x,[e,n])},onEnterCancelled(e){E(e,!1),Bi(w,[e])},onAppearCancelled(e){E(e,!0),Bi(k,[e])},onLeaveCancelled(e){O(e),Bi(S,[e])}})}(e),t);Mi.displayName="Transition";const Ri={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Mi.props=c({},uo,Ri);const Bi=(e,t=[])=>{p(e)?e.forEach((e=>e(...t))):e&&e(...t)},Di=e=>!!e&&(p(e)?e.some((e=>e.length>1)):e.length>1);function ji(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Ni(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Ii]||(e[Ii]=new Set)).add(t)}function Vi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Ii];n&&(n.delete(t),n.size||(e[Ii]=void 0))}function Fi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Hi=0;function qi(e,t,n,o){const r=e._endId=++Hi,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=zi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=zi(a,l);let u=null,d=0,f=0;t===Pi?s>0&&(u=Pi,d=s,f=i.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?Pi:"animation":null,f=u?u===Pi?i.length:l.length:0);const p=u===Pi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,f)}function zi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Wi(t)+Wi(e[n]))))}function Wi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Ui=Symbol("_vod"),Yi=Symbol("_vsh"),Xi={beforeMount(e,{value:t},{transition:n}){e[Ui]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ki(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ki(e,!0),o.enter(e)):o.leave(e,(()=>{Ki(e,!1)})):Ki(e,t))},beforeUnmount(e,{value:t}){Ki(e,t)}};function Ki(e,t){e.style.display=t?e[Ui]:"none",e[Yi]=!t}const Gi=Symbol(""),Zi=/(^|;)\s*display\s*:/;const Ji=/\s*!important$/;function Qi(e,t,n){if(p(n))n.forEach((n=>Qi(e,t,n)));else if(null==n&&(n=""),n=cs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=ts[t];if(n)return n;let o=O(t);if("filter"!==o&&o in e)return ts[t]=o;o=A(o);for(let r=0;r<es.length;r++){const n=es[r]+o;if(n in e)return ts[t]=n}return t}(e,t);Ji.test(n)?e.setProperty($(o),n.replace(Ji,""),"important"):e[o]=n}}const es=["Webkit","Moz","ms"],ts={};const{unit:ns,unitRatio:os,unitPrecision:rs}={unit:"rem",unitRatio:10/320,unitPrecision:5},is=(ss=ns,as=os,ls=rs,e=>e.replace(ye,((e,t)=>{if(!t)return e;if(1===as)return`${t}${ss}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*as,ls);return 0===n?"0":`${n}${ss}`})));var ss,as,ls;const cs=e=>v(e)?is(e):e,us="http://www.w3.org/1999/xlink";const ds=Symbol("_vei");function fs(e,t,n,o,r=null){const i=e[ds]||(e[ds]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(ps.test(e)){let n;for(t={};n=e.match(ps);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):$(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&p(i)){const n=gs(e,i);for(let o=0;o<n.length;o++){const i=n[o];pn(i,t,5,i.__wwe?[e]:r(e))}}else pn(gs(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>hs||(ms.then((()=>hs=0)),hs=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const ps=/(?:Once|Passive|Capture)$/;let hs=0;const ms=Promise.resolve();function gs(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const vs=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const ys=["ctrl","shift","alt","meta"],bs={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>ys.some((n=>e[`${n}Key`]&&!t.includes(n)))},_s=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=bs[t[e]];if(o&&o(n,t))return}return e(n,...o)})},ws=c({patchProp:(e,t,n,o,r,i,s,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;Cn((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Ii];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Qi(o,t,"")}else for(const e in t)null==n[e]&&Qi(o,e,"");for(const e in n)"display"===e&&(i=!0),Qi(o,e,n[e])}else if(r){if(t!==n){const e=o[Gi];e&&(n+=";"+e),o.cssText=n,i=Zi.test(n)}}else t&&e.removeAttribute("style");Ui in e&&(e[Ui]=i?o.display:"",e[Yi]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Qi(o,a,s[a])}(e,n,o):a(t)?l(t)||fs(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&vs(t)&&g(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(vs(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=U(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(us,t.slice(6,t.length)):e.setAttributeNS(us,t,n);else{const o=W(t);null==n||o&&!U(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Ai);let xs;const Ss=(...e)=>{const t=(xs||(xs=Mr(ws))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;g(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Ts="undefined"!=typeof document;const Cs=Object.assign;function ks(e,t){const n={};for(const o in t){const r=t[o];n[o]=Os(r)?r.map(e):e(r)}return n}const Es=()=>{},Os=Array.isArray,Ls=/#/g,$s=/&/g,As=/\//g,Ps=/=/g,Is=/\?/g,Ms=/\+/g,Rs=/%5B/g,Bs=/%5D/g,Ds=/%5E/g,js=/%60/g,Ns=/%7B/g,Vs=/%7C/g,Fs=/%7D/g,Hs=/%20/g;function qs(e){return encodeURI(""+e).replace(Vs,"|").replace(Rs,"[").replace(Bs,"]")}function zs(e){return qs(e).replace(Ms,"%2B").replace(Hs,"+").replace(Ls,"%23").replace($s,"%26").replace(js,"`").replace(Ns,"{").replace(Fs,"}").replace(Ds,"^")}function Ws(e){return null==e?"":function(e){return qs(e).replace(Ls,"%23").replace(Is,"%3F")}(e).replace(As,"%2F")}function Us(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ys=/\/$/;function Xs(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Us(s)}}function Ks(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Gs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Zs(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Js(e[n],t[n]))return!1;return!0}function Js(e,t){return Os(e)?Qs(e,t):Os(t)?Qs(t,e):e===t}function Qs(e,t){return Os(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var ea,ta,na,oa;function ra(e){if(!e)if(Ts){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ys,"")}(ta=ea||(ea={})).pop="pop",ta.push="push",(oa=na||(na={})).back="back",oa.forward="forward",oa.unknown="";const ia=/^[^#]+#/;function sa(e,t){return e.replace(ia,"#")+t}const aa=()=>({left:window.scrollX,top:window.scrollY});function la(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function ca(e,t){return(history.state?history.state.position-t:-1)+e}const ua=new Map;function da(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Ks(n,"")}return Ks(n,e)+o+r}function fa(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?aa():null}}function pa(e){const{history:t,location:n}=window,o={value:da(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Cs({},r.value,t.state,{forward:e,scroll:aa()});i(s.current,s,!0),i(e,Cs({},fa(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Cs({},t.state,fa(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ha(e){const t=pa(e=ra(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=da(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:ea.pop,direction:u?u>0?na.forward:na.back:na.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Cs({},e.state,{scroll:aa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Cs({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:sa.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ma(e){return"string"==typeof e||"symbol"==typeof e}const ga={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},va=Symbol("");var ya,ba;function _a(e,t){return Cs(new Error,{type:e,[va]:!0},t)}function wa(e,t){return e instanceof Error&&va in e&&(null==t||!!(e.type&t))}(ba=ya||(ya={}))[ba.aborted=4]="aborted",ba[ba.cancelled=8]="cancelled",ba[ba.duplicated=16]="duplicated";const xa={sensitive:!1,strict:!1,start:!0,end:!0},Sa=/[.+*?^${}()[\]/\\]/g;function Ta(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ca(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=Ta(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(ka(o))return 1;if(ka(r))return-1}return r.length-o.length}function ka(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ea={type:0,value:""},Oa=/[a-zA-Z0-9_]/;function La(e,t,n){const o=function(e,t){const n=Cs({},xa,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(Sa,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(Os(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Os(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Ea]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function f(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:Oa.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=Cs(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function $a(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Pa(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Ra(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Cs({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=La(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),a&&e.name&&!Ia(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&s(d)}return f?()=>{i(f)}:Es}function i(e){if(ma(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&Ca(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Ba(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Ia(e)&&o.set(e.record.name,e)}return t=Ra({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw _a(1,{location:e});s=r.record.name,a=Cs(Aa(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Aa(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw _a(1,{location:e,currentLocation:t});s=r.record.name,a=Cs({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Ma(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Aa(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Pa(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Ia(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ma(e){return e.reduce(((e,t)=>Cs(e,t.meta)),{})}function Ra(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ba(e,t){return t.children.some((t=>t===e||Ba(e,t)))}function Da(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ms," "),r=e.indexOf("="),i=Us(r<0?e:e.slice(0,r)),s=r<0?null:Us(e.slice(r+1));if(i in t){let e=t[i];Os(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function ja(e){let t="";for(let n in e){const o=e[n];if(n=zs(n).replace(Ps,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Os(o)?o.map((e=>e&&zs(e))):[o&&zs(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Na(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Os(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Va=Symbol(""),Fa=Symbol(""),Ha=Symbol(""),qa=Symbol(""),za=Symbol("");function Wa(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Ua(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(_a(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(_a(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Ya(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Ua(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Ua(c,n,o,a,e,r)()}))))}}var s;return i}function Xa(e){const t=br(Ha),n=br(qa),o=ki((()=>t.resolve(cn(e.to)))),r=ki((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Gs.bind(null,r));if(s>-1)return s;const a=Ga(e[t-2]);return t>1&&Ga(r)===a&&i[i.length-1].path!==a?i.findIndex(Gs.bind(null,e[t-2])):s})),i=ki((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Os(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=ki((()=>r.value>-1&&r.value===n.matched.length-1&&Zs(n.params,o.value.params)));return{route:o,href:ki((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[cn(e.replace)?"replace":"push"](cn(e.to)).catch(Es):Promise.resolve()}}}const Ka=bo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Xa,setup(e,{slots:t}){const n=qt(Xa(e)),{options:o}=br(Ha),r=ki((()=>({[Za(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Za(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ei("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Ga(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Za=(e,t,n)=>null!=e?e:null!=t?t:n;function Ja(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Qa=bo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=br(za),r=ki((()=>e.route||o.value)),i=br(Fa,0),s=ki((()=>{let e=cn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=ki((()=>r.value.matched[s.value]));yr(Fa,ki((()=>s.value+1))),yr(Va,a),yr(za,r);const l=sn();return eo((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Gs(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return Ja(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Ei(c,Cs({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return Ja(n.default,{Component:f,route:o})||f}}});function el(e){const t=$a(e.routes,e),n=e.parseQuery||Da,o=e.stringifyQuery||ja,r=e.history,i=Wa(),s=Wa(),a=Wa(),l=an(ga,!0);let c=ga;Ts&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ks.bind(null,(e=>""+e)),d=ks.bind(null,Ws),f=ks.bind(null,Us);function p(e,i){if(i=Cs({},i||l.value),"string"==typeof e){const o=Xs(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Cs(o,s,{params:f(s.params),hash:Us(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=Cs({},e,{path:Xs(n,e.path,i.path).path});else{const t=Cs({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Cs({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(f(a.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Cs({},e,{hash:(h=c,qs(h).replace(Ns,"{").replace(Fs,"}").replace(Ds,"^")),path:a.path}));var h;const m=r.createHref(p);return Cs({fullPath:p,hash:c,query:o===ja?Na(e.query):e.query||{}},a,{redirectedFrom:void 0,href:m})}function h(e){return"string"==typeof e?Xs(n,e,l.value.path):Cs({},e)}function m(e,t){if(c!==e)return _a(8,{from:t,to:e})}function g(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Cs({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=p(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Cs(h(u),{state:"object"==typeof u?Cs({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let f;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Gs(t.matched[o],n.matched[r])&&Zs(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=_a(16,{to:d,from:r}),A(r,r,!0,!1)),(f?Promise.resolve(f):w(d,r)).catch((e=>wa(e)?wa(e,2)?e:$(e):L(e,d,r))).then((e=>{if(e){if(wa(e,2))return y(Cs({replace:a},h(e.to),{state:"object"==typeof e.to?Cs({},i,e.to.state):i,force:s}),t||d)}else e=S(d,r,!0,a,i);return x(d,r,e),e}))}function b(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=M.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Gs(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Gs(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Ya(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Ua(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),B(n).then((()=>{n=[];for(const o of i.list())n.push(Ua(o,e,t));return n.push(l),B(n)})).then((()=>{n=Ya(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Ua(o,e,t))}));return n.push(l),B(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(Os(o.beforeEnter))for(const r of o.beforeEnter)n.push(Ua(r,e,t));else n.push(Ua(o.beforeEnter,e,t));return n.push(l),B(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ya(a,"beforeRouteEnter",e,t,_),n.push(l),B(n)))).then((()=>{n=[];for(const o of s.list())n.push(Ua(o,e,t));return n.push(l),B(n)})).catch((e=>wa(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>_((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=m(e,t);if(s)return s;const a=t===ga,c=Ts?history.state:{};n&&(o||a?r.replace(e.fullPath,Cs({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,A(e,t,n,a),$()}let T;function C(){T||(T=r.listen(((e,t,n)=>{if(!R.listening)return;const o=p(e),i=v(o);if(i)return void y(Cs(i,{replace:!0}),o).catch(Es);c=o;const s=l.value;var a,u;Ts&&(a=ca(s.fullPath,n.delta),u=aa(),ua.set(a,u)),w(o,s).catch((e=>wa(e,12)?e:wa(e,2)?(y(e.to,o).then((e=>{wa(e,20)&&!n.delta&&n.type===ea.pop&&r.go(-1,!1)})).catch(Es),Promise.reject()):(n.delta&&r.go(-n.delta,!1),L(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!wa(e,8)?r.go(-n.delta,!1):n.type===ea.pop&&wa(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(Es)})))}let k,E=Wa(),O=Wa();function L(e,t,n){$(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function $(e){return k||(k=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function A(t,n,o,r){const{scrollBehavior:i}=e;if(!Ts||!i)return Promise.resolve();const s=!o&&function(e){const t=ua.get(e);return ua.delete(e),t}(ca(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Cn().then((()=>i(t,n,s))).then((e=>e&&la(e))).catch((e=>L(e,t,n)))}const P=e=>r.go(e);let I;const M=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ma(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:g,replace:function(e){return g(Cs(h(e),{replace:!0}))},go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return k&&l.value!==ga?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Ka),e.component("RouterView",Qa),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>cn(l)}),Ts&&!I&&l.value===ga&&(I=!0,g(r.location).catch((e=>{})));const t={};for(const o in ga)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Ha,this),e.provide(qa,zt(t)),e.provide(za,l);const n=e.unmount;M.add(e),e.unmount=function(){M.delete(e),M.size<1&&(c=ga,T&&T(),T=null,l.value=ga,I=!1,k=!1),n()}}};function B(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return R}function tl(){return br(qa)}const nl=["{","}"];const ol=/^(?:\d)+/,rl=/^(?:\w)+/;const il=Object.prototype.hasOwnProperty,sl=(e,t)=>il.call(e,t),al=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=nl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=ol.test(t)?"list":a&&rl.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function ll(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class cl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||al,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=ll(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{sl(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=ll(t,this.messages))&&(o=this.messages[t]):n=t,sl(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function ul(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Od?Od():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new cl({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Gh().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function dl(e,t){return e.indexOf(t[0])>-1}const fl=ie((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let pl;function hl(e){return dl(e,ee)?vl().f(e,function(){const e=Od(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ee):e}function ml(e,t){if(1===t.length){if(e){const n=e=>v(e)&&dl(e,ee),o=t[0];let r=[];if(p(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return ml(e&&e[n],t)}function gl(e,t){const n=ml(e,t);if(!n)return!1;const o=t[t.length-1];if(p(n))n.forEach((e=>gl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>hl(e),set(t){e=t}})}return!0}function vl(){if(!pl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,pl=ul(e),fl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>pl.add(e,__uniConfig.locales[e]))),pl.setLocale(e)}}return pl}function yl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const bl=ie((()=>{const e="uni.async.",t=["error"];vl().add("en",yl(e,t,["The connection timed out, click the screen to try again."]),!1),vl().add("es",yl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),vl().add("fr",yl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),vl().add("zh-Hans",yl(e,t,["连接服务器超时，点击屏幕重试"]),!1),vl().add("zh-Hant",yl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),_l=ie((()=>{const e="uni.showToast.",t=["unpaired"];vl().add("en",yl(e,t,["Please note showToast must be paired with hideToast"]),!1),vl().add("es",yl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),vl().add("fr",yl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),vl().add("zh-Hans",yl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),vl().add("zh-Hant",yl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),wl=ie((()=>{const e="uni.showLoading.",t=["unpaired"];vl().add("en",yl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),vl().add("es",yl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),vl().add("fr",yl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),vl().add("zh-Hans",yl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),vl().add("zh-Hant",yl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),xl=ie((()=>{const e="uni.showModal.",t=["cancel","confirm"];vl().add("en",yl(e,t,["Cancel","OK"]),!1),vl().add("es",yl(e,t,["Cancelar","OK"]),!1),vl().add("fr",yl(e,t,["Annuler","OK"]),!1),vl().add("zh-Hans",yl(e,t,["取消","确定"]),!1),vl().add("zh-Hant",yl(e,t,["取消","確定"]),!1)})),Sl=ie((()=>{const e="uni.chooseFile.",t=["notUserActivation"];vl().add("en",yl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),vl().add("es",yl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),vl().add("fr",yl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),vl().add("zh-Hans",yl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),vl().add("zh-Hant",yl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Tl=ie((()=>{const e="uni.setClipboardData.",t=["success","fail"];vl().add("en",yl(e,t,["Content copied","Copy failed, please copy manually"]),!1),vl().add("es",yl(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),vl().add("fr",yl(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),vl().add("zh-Hans",yl(e,t,["内容已复制","复制失败，请手动复制"]),!1),vl().add("zh-Hant",yl(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),Cl=ie((()=>{const e="uni.picker.",t=["done","cancel"];vl().add("en",yl(e,t,["Done","Cancel"]),!1),vl().add("es",yl(e,t,["OK","Cancelar"]),!1),vl().add("fr",yl(e,t,["OK","Annuler"]),!1),vl().add("zh-Hans",yl(e,t,["完成","取消"]),!1),vl().add("zh-Hant",yl(e,t,["完成","取消"]),!1)}));function kl(e){const t=new Pe;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let El=1;const Ol=Object.create(null);function Ll(e,t){return e+"."+t}function $l(e,t,n){t=Ll(e,t),Ol[t]||(Ol[t]=n)}function Al({id:e,name:t,args:n},o){t=Ll(o,t);const r=t=>{e&&jg.publishHandler("invokeViewApi."+e,t)},i=Ol[t];i?i(n,r):r({})}const Pl=c(kl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=jg,i=n?El++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Il=be(!0);let Ml;function Rl(){Ml&&(clearTimeout(Ml),Ml=null)}let Bl=0,Dl=0;function jl(e){if(Rl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Bl=t,Dl=n,Ml=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Nl(e){if(!Ml)return;if(1!==e.touches.length)return Rl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Bl)>10||Math.abs(n-Dl)>10?Rl():void 0}function Vl(e,t){const n=Number(e);return isNaN(n)?t:n}function Fl(){const e=__uniConfig.globalStyle||{},t=Vl(e.rpxCalcMaxDeviceWidth,960),n=Vl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Hl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ql,zl,Wl=["top","left","right","bottom"],Ul={};function Yl(){return zl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Xl(){if(zl="string"==typeof zl?zl:Yl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Wl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),ql=!0}else Wl.forEach((function(e){Ul[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:zl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Gl.length||setTimeout((function(){var e={};Gl.forEach((function(t){e[t]=Ul[t]})),Gl.length=0,Zl.forEach((function(t){t(e)}))}),0);Gl.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Ul,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Kl(e){return ql||Xl(),Ul[e]}var Gl=[];var Zl=[];const Jl=Hl({get support(){return 0!=("string"==typeof zl?zl:Yl()).length},get top(){return Kl("top")},get left(){return Kl("left")},get right(){return Kl("right")},get bottom(){return Kl("bottom")},onChange:function(e){Yl()&&(ql||Xl(),"function"==typeof e&&Zl.push(e))},offChange:function(e){var t=Zl.indexOf(e);t>=0&&Zl.splice(t,1)}}),Ql=_s((()=>{}),["prevent"]),ec=_s((e=>{}),["stop"]);function tc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function nc(){const e=tc(document.documentElement.style,"--window-top");return e?e+Jl.top:0}function oc(){const e=document.documentElement.style,t=nc(),n=tc(e,"--window-bottom"),o=tc(e,"--window-left"),r=tc(e,"--window-right"),i=tc(e,"--top-window-height");return{top:t,bottom:n?n+Jl.bottom:0,left:o?o+Jl.left:0,right:r?r+Jl.right:0,topWindowHeight:i||0}}function rc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function ic(e){return rc(e)}function sc(e){return Symbol(e)}function ac(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function lc(e,t=!1){if(t)return function(e){if(!ac(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>ad(parseFloat(t))+"px"))}(e);if(v(e)){const t=parseInt(e)||0;return ac(e)?ad(t):t}return e}function cc(e){return e.$page}function uc(e){return 0===e.tagName.indexOf("UNI-")}const dc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",fc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",pc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z";function hc(e,t="#000",n=27){return oi("svg",{width:n,height:n,viewBox:"0 0 32 32"},[oi("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function mc(){{const{$pageInstance:e}=mi();return e&&xc(e.proxy)}}function gc(){const e=Cf(),t=e.length;if(t)return e[t-1]}function vc(){var e;const t=null==(e=gc())?void 0:e.$page;if(t)return t.meta}function yc(){const e=vc();return e?e.id:-1}function bc(){const e=gc();if(e)return e.$vm}const _c=["navigationBar","pullToRefresh"];function wc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);_c.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function xc(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function Sc(e,t,n){if(v(e))n=t,t=e,e=bc();else if("number"==typeof e){const t=Cf().find((t=>cc(t).id===e));e=t?t.$vm:bc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Tc(e){e.preventDefault()}let Cc,kc=0;function Ec({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-kc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(kc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(Cc=setTimeout(s,300))),o=!1};return function(){clearTimeout(Cc),o||requestAnimationFrame(s),o=!0}}function Oc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Oc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),re(i.concat(n).join("/"))}function Lc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function $c(){Fl(),ge(uc),window.addEventListener("touchstart",jl,Il),window.addEventListener("touchmove",Nl,Il),window.addEventListener("touchend",Rl,Il),window.addEventListener("touchcancel",Rl,Il)}class Ac{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(fe(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&fe(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Rc(this.$el.querySelector(e));return t?Pc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Rc(n[o]);e&&t.push(Pc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:$(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=q(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];g(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&jg.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Pc(e,t=!0){if(t&&e&&(e=de(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Ac(e)),e.$el.__wxsComponentDescriptor}function Ic(e,t){return Pc(e,t)}function Mc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Ic(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=de(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Ic(r,!1)]}}function Rc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Bc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=_e(t?r:function(e){for(;!uc(e);)e=e.parentElement;return e}(r)),a=_e(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&S(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Dc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function jc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Nc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!uc(o);if(r)return Mc(e,t,n,!1)||[e];const i=Bc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=nc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Dc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=nc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Dc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=nc();i.touches=jc(e.touches,t),i.changedTouches=jc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Mc(i,t,n)||[i]},createNativeEvent:Bc},Symbol.toStringTag,{value:"Module"});function Vc(e){!function(e){const t=e.globalProperties;c(t,Nc),t.$gcd=Ic}(e._context.config)}let Fc=1;function Hc(e){return(e||yc())+".invokeViewApi"}const qc=c(kl("view"),{invokeOnCallback:(e,t)=>Ng.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Ng,s=o?Fc++:0;o&&r("invokeViewApi."+s,o,!0),i(Hc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Ng,a=Fc++,l="invokeViewApi."+a;return r(l,n),s(Hc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function zc(e){Sc(gc(),"onResize",e),Ng.invokeOnCallback("onWindowResize",e)}function Wc(e){const t=gc();Sc(Gh(),"onShow",e),Sc(t,"onShow")}function Uc(){Sc(Gh(),"onHide"),Sc(gc(),"onHide")}const Yc=["onPageScroll","onReachBottom"];function Xc(){Yc.forEach((e=>Ng.subscribe(e,function(e){return(t,n)=>{Sc(parseInt(n),e,t)}}(e))))}function Kc(){!function(){const{on:e}=Ng;e("onResize",zc),e("onAppEnterForeground",Wc),e("onAppEnterBackground",Uc)}(),Xc()}function Gc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new ke(this.$page.id)),e.eventChannel}}function Zc(e){e._context.config.globalProperties.getOpenerEventChannel=Gc}function Jc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function Qc(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${ad(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function eu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(Qc)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?Qc(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const tu={props:["animation"],watch:{animation:{deep:!0,handler(){eu(this)}}},mounted(){eu(this)}},nu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(tu),ou(e)},ou=e=>(e.__reserved=!0,e.compatConfig={MODE:3},bo(e));function ru(e){return e.__wwe=!0,e}function iu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=_e(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const su={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function au(e){const t=sn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:ru((function(e){e.touches.length>1||s(e)})),onMousedown:ru((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:ru((function(){a()})),onMouseup:ru((function(){r&&l()})),onTouchcancel:ru((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function lu(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const cu=sc("uf"),uu={for:{type:String,default:""}},du=sc("ul");const fu=nu({name:"Label",props:uu,setup(e,{slots:t}){const n=sn(null),o=mc(),r=function(){const e=[];return yr(du,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),i=ki((()=>e.for||t.default&&t.default.length)),s=ru((t=>{const n=t.target;let i=/^uni-(checkbox|radio|switch)-/.test(n.className);i||(i=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(n.tagName)),i||(e.for?jg.emit("uni-label-click-"+o+"-"+e.for,t,!0):r.length&&r[0](t,!0))}));return()=>oi("uni-label",{ref:n,class:{"uni-label-pointer":i},onClick:s},[t.default&&t.default()],10,["onClick"])}});function pu(e,t){hu(e.id,t),eo((()=>e.id),((e,n)=>{mu(n,t,!0),hu(e,t,!0)})),qo((()=>{mu(e.id,t)}))}function hu(e,t,n){const o=mc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&jg.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?jg.on(r,t[r]):e&&jg.on(`uni-${r}-${o}-${e}`,t[r])}))}function mu(e,t,n){const o=mc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&jg.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?jg.off(r,t[r]):e&&jg.off(`uni-${r}-${o}-${e}`,t[r])}))}const gu=nu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null),o=br(cu,!1),{hovering:r,binding:i}=au(e),s=ru(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=br(du,!1);return a&&(a.addHandler(s),Ho((()=>{a.removeHandler(s)}))),pu(e,{"label-click":s}),()=>{const o=e.hoverClass,a=lu(e,"disabled"),l=lu(e,"loading"),c=lu(e,"plain"),u=o&&"none"!==o;return oi("uni-button",ui({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),vu=sc("upm");function yu(){return br(vu)}function bu(e){const t=function(e){return qt(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=lc(e.offset)),e.height&&(e.height=lc(e.height)),e.range&&(e.range=lc(e.range)),e}(c({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+Jl.top),e.pullToRefresh=t}}{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Cf().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(wc(tl().meta,e)))))}(e);return yr(vu,t),t}function _u(){return tl()}function wu(){return history.state&&history.state.__id__||1}const xu=["original","compressed"],Su=["album","camera"],Tu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Cu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function ku(e,t){return!p(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Eu(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Ou=1;const Lu={};function $u(e,t,n){if("number"==typeof e){const o=Lu[e];if(o)return o.keepAlive||delete Lu[e],o.callback(t,n)}return t}const Au="success",Pu="fail",Iu="complete";function Mu(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];g(o)&&(t[n]=Eu(o),delete e[n])}return t}(t),a=g(r),l=g(i),c=g(s),u=Ou++;return function(e,t,n,o=!1){Lu[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),g(n)&&n(u),u.errMsg===e+":ok"?(g(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Ru="success",Bu="fail",Du="complete",ju={},Nu={};function Vu(e,t){return function(n){return e(n,t)||n}}function Fu(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Vu(i,n));else{const e=i(t,n);if(_(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Hu(e,t={}){return[Ru,Bu,Du].forEach((n=>{const o=e[n];if(!p(o))return;const r=t[n];t[n]=function(e){Fu(o,e,t).then((e=>g(r)&&r(e)||e))}})),t}function qu(e,t){const n=[];p(ju.returnValue)&&n.push(...ju.returnValue);const o=Nu[e];return o&&p(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function zu(e){const t=Object.create(null);Object.keys(ju).forEach((e=>{"returnValue"!==e&&(t[e]=ju[e].slice())}));const n=Nu[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Wu(e,t,n,o){const r=zu(e);if(r&&Object.keys(r).length){if(p(r.invoke)){return Fu(r.invoke,n).then((n=>t(Hu(zu(e),n),...o)))}return t(Hu(r,n),...o)}return t(n,...o)}function Uu(e,t){return(n={},...o)=>function(e){return!(!S(e)||![Au,Pu,Iu].find((t=>g(e[t]))))}(n)?qu(e,Wu(e,t,c({},n),o)):qu(e,new Promise(((r,i)=>{Wu(e,t,c({},n,{success:r,fail:i}),o)})))}function Yu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,$u(e,c({errMsg:i},o))}function Xu(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(g(s)){const o=s(e[0][t],n);if(v(o))return o}else f(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Ku(e,t,n,o){return n=>{const r=Mu(e,n,o),i=Xu(0,[n],0,o);return i?Yu(r,e,i):t(n,{resolve:t=>function(e,t,n){return $u(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Yu(r,e,function(e){return!e||v(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Gu(e,t,n,o){return Uu(e,Ku(e,t,0,o))}function Zu(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Xu(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Ju(e,t,n,o){return Uu(e,function(e,t,n,o){return Ku(e,t,0,o)}(e,t,0,o))}let Qu=!1,ed=0,td=0,nd=960,od=375,rd=750;function id(){let e,t,n;{const{windowWidth:o,pixelRatio:r,platform:i}=function(){const e=Gf(),t=Qf(Jf(e,Zf(e)));return{platform:Wf?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();e=o,t=r,n=i}ed=e,td=t,Qu="ios"===n}function sd(e,t){const n=Number(e);return isNaN(n)?t:n}const ad=Zu(0,((e,t)=>{if(0===ed&&(id(),function(){const e=__uniConfig.globalStyle||{};nd=sd(e.rpxCalcMaxDeviceWidth,960),od=sd(e.rpxCalcBaseDeviceWidth,375),rd=sd(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||ed;n=e===rd||n<=nd?n:od;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==td&&Qu?.5:1),e<0?-o:o})),ld=[.5,.8,1,1.25,1.5,2];const cd=(e,t,n,o)=>{!function(e,t,n,o,r){Ng.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};const ud={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function dd(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(f(ud,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(ud[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class fd{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,dd(t)])}}class pd{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class hd{constructor(e){this.width=e}}let md=0,gd={};function vd(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(md++);r.callbackId=e,gd[e]=o}Ng.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},v(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?g(e.success)&&e.success(t):g(e.fail)&&e.fail(t),g(e.complete)&&e.complete(t)}(gd[e],t),delete gd[e])}))}const yd={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,s,a,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){Ng.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new fd("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new fd("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new pd(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new hd(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+\.?\d*r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal","lighter","bolder"].indexOf(e)>-1||/^\d+$/.test(e)?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){cd(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){cd(this.id,this.pageId,"moveToLocation",e)}getScale(e){cd(this.id,this.pageId,"getScale",e)}getRegion(e){cd(this.id,this.pageId,"getRegion",e)}includePoints(e){cd(this.id,this.pageId,"includePoints",e)}translateMarker(e){cd(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){cd(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){cd(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){cd(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){cd(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){cd(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){cd(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){cd(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){cd(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){cd(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){cd(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){cd(this.id,this.pageId,"openMapApp",e)}on(e,t){cd(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){ep(this.id,this.pageId,"play")}pause(){ep(this.id,this.pageId,"pause")}stop(){ep(this.id,this.pageId,"stop")}seek(e){ep(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){ep(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~ld.indexOf(e)||(e=1),ep(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){ep(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){ep(this.id,this.pageId,"exitFullScreen")}showStatusBar(){ep(this.id,this.pageId,"showStatusBar")}hideStatusBar(){ep(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){vd(this.id,this.pageId,e,t)}}};function bd(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=yd[n];e.context=new r(t,o),delete e.contextInfo}}class _d{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class wd{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):np(i,n)?i:i.querySelector(n);return e?tp(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(tp(t,r))})),!l&&np(i,n)&&e.unshift(tp(i,r)),e}}(e,t,n,r,i))})),n(o)}(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{p(e)?e.forEach(bd):bd(e);const o=n[t];g(o)&&o.call(this,e)})),g(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=ue(e),this}select(e){return this._nodesRef=new _d(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new _d(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new _d(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const xd=Zu(0,(e=>((e=ue(e))&&!function(e){const t=ue(e);if(t.$page)return xc(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return xc(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?xc(n):void 0}(e)&&(e=null),new wd(e||bc())))),Sd={formatArgs:{}},Td={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Cd{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=c({},Td,e)}_getOption(e){const t={transition:c({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const kd=ie((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Cd.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Ed=Zu(0,(e=>(kd(),new Cd(e))),0,Sd),Od=Zu(0,(()=>{const e=Gh();return e&&e.$vm?e.$vm.$locale:vl().getLocale()})),Ld={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const $d={formatArgs:{showToast:!0},beforeInvoke(){Tl()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=vl(),o=n("uni.setClipboardData.success");o&&fg({title:o,icon:"success",mask:!1})}},Ad=(Boolean,{formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=ku(e,xu)},sourceType(e,t){t.sourceType=ku(e,Su)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}}),Pd="json",Id=["text","arraybuffer"],Md=encodeURIComponent;ArrayBuffer,Boolean;const Rd={formatArgs:{method(e,t){t.method=Cu((e||"").toUpperCase(),Tu)},data(e,t){t.data=e||""},url(e,t){t.method===Tu[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(f(t,a)){let e=t[a];null==e?e="":S(e)&&(e=JSON.stringify(e)),s[Md(a)]=Md(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Tu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Pd).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Id.indexOf(t.responseType)&&(t.responseType="text")}}},Bd={formatArgs:{filePath(e,t){e&&(t.filePath=Hf(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const Dd={url:{type:String,required:!0}},jd=(Hd(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Hd(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Wd("navigateTo")),Nd=Wd("redirectTo"),Vd=Wd("reLaunch"),Fd={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Cf().length-1,e)}}};function Hd(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let qd;function zd(){qd=""}function Wd(e){return{formatArgs:{url:Ud(e)},beforeAll:zd}}function Ud(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=Cf();return n.length&&(t=cc(n[n.length-1]).route),Oc(t,e)}(t)).split("?")[0],r=Lc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(qd===t&&"appLaunch"!==n.openType)return`${qd} locked`;__uniConfig.ready&&(qd=t)}else if(r.meta.isTabBar){const e=Cf(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}Boolean;const Yd={formatArgs:{title:"",mask:!1}},Xd=(Boolean,{beforeInvoke(){xl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!f(t,"cancelText")){const{t:e}=vl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!f(t,"confirmText")){const{t:e}=vl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Kd=["success","loading","none","error"],Gd=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Cu(e,Kd)},image(e,t){t.image=e?Hf(e):""},duration:1500,mask:!1}});function Zd(){const e=bc();if(!e)return;const t=Tf(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Ef(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Sc(e,"onHide"))}function Jd(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function Qd(e){const t=Tf().values();for(const n of t){const t=yf(n);if(Jd(e,t))return n.$.__isActive=!0,t.id}}const ef=Ju("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(bf.handledBeforeEntryPageRoutes)return Zd(),sf({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},Qd(e)).then(o).catch(r);wf.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Wd("switchTab"));function tf(){const e=gc();if(!e)return;const t=yf(e);Ef(Af(t.path,t.id))}const nf=Ju("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(bf.handledBeforeEntryPageRoutes)return tf(),sf({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o);xf.push({args:{type:"redirectTo",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Nd);function of(){const e=Tf().keys();for(const t of e)Ef(t)}const rf=Ju("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(bf.handledBeforeEntryPageRoutes)return of(),sf({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);Sf.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Vd);function sf({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Gh().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Te(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++Of,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(wa(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new ke(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function af(){if(bf.handledBeforeEntryPageRoutes)return;bf.handledBeforeEntryPageRoutes=!0;const e=[..._f];_f.length=0,e.forEach((({args:e,resolve:t,reject:n})=>sf(e).then(t).catch(n)));const t=[...wf];wf.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(Zd(),sf(e,Qd(e.url)).then(t).catch(n))));const n=[...xf];xf.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(tf(),sf(e).then(t).catch(n))));const o=[...Sf];Sf.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(of(),sf(e).then(t).catch(n))))}let lf;function cf(){var e;return lf||(lf=__uniConfig.tabBar&&qt((e=__uniConfig.tabBar,fl()&&e.list&&e.list.forEach((e=>{gl(e,["text"])})),e))),lf}function uf(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const df=uf("top:env(a)"),ff=uf("top:constant(a)"),pf=uf("backdrop-filter:blur(10px)"),hf=(()=>df?"env":ff?"constant":"")();function mf(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=cf();e.shown&&(n=parseInt(e.height))}var o;ic({"--window-top":(o=t,hf?`calc(${o}px + ${hf}(safe-area-inset-top))`:`${o}px`),"--window-bottom":gf(n)})}function gf(e){return hf?`calc(${e}px + ${hf}(safe-area-inset-bottom))`:`${e}px`}const vf=new Map;function yf(e){return e.$page}const bf={handledBeforeEntryPageRoutes:!1},_f=[],wf=[],xf=[],Sf=[];function Tf(){return vf}function Cf(){return kf()}function kf(){const e=[],t=vf.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Ef(e,t=!0){const n=vf.get(e);n.$.__isUnload=!0,Sc(n,"onUnload"),vf.delete(e),t&&function(e){const t=Pf.get(e);t&&(Pf.delete(e),If.pruneCacheEntry(t))}(e)}let Of=wu();function Lf(e){const t=yu();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Re(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:re(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function $f(e){const t=Lf(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),vf.set(Af(t.path,t.id),e),1===vf.size&&setTimeout((()=>{af()}),0)}function Af(e,t){return e+"$$"+t}const Pf=new Map,If={get:e=>Pf.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;If.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;If.delete(n),If.pruneCacheEntry(e),Cn((()=>{vf.forEach(((e,t)=>{e.$.isUnmounted&&vf.delete(t)}))}))}}))}(e),Pf.set(e,t)},delete(e){Pf.get(e)&&Pf.delete(e)},forEach(e){Pf.forEach(e)}};function Mf(e,t){!function(e){const t=Bf(e),{body:n}=document;Df&&n.removeAttribute(Df),t&&n.setAttribute(t,""),Df=t}(e),mf(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Vf(e,t)}function Rf(e){const t=Bf(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Bf(e){return e.type.__scopeId}let Df;const jf=!!(()=>{let e=!1;try{const t={};Object.defineProperty(t,"passive",{get(){e=!0}}),window.addEventListener("test-passive",(()=>{}),t)}catch(t){}return e})()&&{passive:!1};let Nf;function Vf(e,t){if(document.removeEventListener("touchmove",Tc),Nf&&document.removeEventListener("scroll",Nf),t.disableScroll)return document.addEventListener("touchmove",Tc,jf);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=yf(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&jg.publishHandler("onPageScroll",{scrollTop:o},e),n&&jg.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>jg.publishHandler("onReachBottom",{},s)),Nf=Ec(i),requestAnimationFrame((()=>document.addEventListener("scroll",Nf)))}function Ff(e){const{base:t}=__uniConfig.router;return 0===re(e).indexOf(t)?re(e):t+e}function Hf(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Ff(e.slice(1));e="https:"+e}if(te.test(e)||ne.test(e)||0===e.indexOf("blob:"))return e;const o=kf();return o.length?Ff(Oc(yf(o[o.length-1]).route,e).slice(1)):e}const qf=navigator.userAgent,zf=/android/i.test(qf),Wf=/iphone|ipad|ipod/i.test(qf),Uf=qf.match(/Windows NT ([\d|\d.\d]*)/i),Yf=/Macintosh|Mac/i.test(qf),Xf=/Linux|X11/i.test(qf),Kf=Yf&&navigator.maxTouchPoints>0;function Gf(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Zf(e){return e&&90===Math.abs(window.orientation)}function Jf(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Qf(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function ep(e,t,n,o){Ng.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function tp(e,t){const n={},{top:o,topWindowHeight:r}=oc();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=ve(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(p(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(p(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function np(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const op={};function rp(e,t){const n=op[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return ip(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function ip(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function sp(e){for(const n in op)if(f(op,n)){if(op[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return op[t]=e,t}const ap=Jc(),lp=Jc();const cp=nu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=sn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=qt({width:-1,height:-1});return eo((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){Oo(o),No((()=>{t.initial&&Cn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>oi("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[oi("div",{onScroll:r},[oi("div",null,null)],40,["onScroll"]),oi("div",{onScroll:r},[oi("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const up=sc("ucg"),dp=nu({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,{emit:t,slots:n}){const o=sn(null);return function(e,t){const n=[],o=()=>n.reduce(((e,t)=>(t.value.checkboxChecked&&e.push(t.value.value),e)),new Array);yr(up,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},checkboxChange(e){t("change",e,{value:o()})}});const r=br(cu,!1);r&&r.addField({submit:()=>{let t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=o()),t}})}(e,iu(o,t)),()=>oi("uni-checkbox-group",{ref:o},[n.default&&n.default()],512)}});const fp=nu({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""},foreColor:{type:String,default:""}},setup(e,{slots:t}){const n=sn(null),o=sn(e.checked),r=ki((()=>"true"===o.value||!0===o.value)),i=sn(e.value);const s=ki((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const n={};return t?(e.activeBorderColor&&(n.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(n.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(r.value)));eo([()=>e.checked,()=>e.value],(([e,t])=>{o.value=e,i.value=t}));const{uniCheckGroup:a,uniLabel:l}=function(e,t,n){const o=ki((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),r={reset:n},i=br(up,!1);i&&i.addField(o);const s=br(cu,!1);s&&s.addField(r);const a=br(du,!1);return Ho((()=>{i&&i.removeField(o),s&&s.removeField(r)})),{uniCheckGroup:i,uniForm:s,uniLabel:a}}(o,i,(()=>{o.value=!1})),c=t=>{e.disabled||(o.value=!o.value,a&&a.checkboxChange(t),t.stopPropagation())};return l&&(l.addHandler(c),Ho((()=>{l.removeHandler(c)}))),pu(e,{"label-click":c}),()=>{const r=lu(e,"disabled");let i;return i=o.value,oi("uni-checkbox",ui(r,{id:e.id,onClick:c,ref:n}),[oi("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[oi("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:s.value},[i?hc(dc,e.disabled?"#ADADAD":e.foreColor||e.iconColor||e.color,22):""],6),t.default&&t.default()],4)],16,["id","onClick"])}}});function pp(){}const hp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function mp(e,t,n){function o(e){const t=ki((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",pp,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",pp,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}eo((()=>t.value),(e=>e&&o(e)))}var gp=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,vp=/^<\/([-A-Za-z0-9_]+)[^>]*>/,yp=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,bp=Cp("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),_p=Cp("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),wp=Cp("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),xp=Cp("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Sp=Cp("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Tp=Cp("script,style");function Cp(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const kp={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Ep={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Op={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Lp=nu({name:"Image",props:kp,setup(e,{emit:t}){const n=sn(null),o=function(e,t){const n=sn(""),o=ki((()=>{let e="auto",o="";const r=Op[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=qt({rootEl:e,src:ki((()=>t.src?Hf(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return No((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=iu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Ep[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){$p&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return eo((()=>t.mode),((e,t)=>{Ep[t]&&r(),Ep[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),Cn((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};eo((()=>e.src),(e=>l(e))),eo((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),No((()=>l(e.src))),Ho((()=>c()))}(o,e,n,i,r),()=>oi("uni-image",{ref:n},[oi("div",{style:o.modeStyle},null,4),Ep[e.mode]?oi(cp,{onResize:i},null,8,["onResize"]):oi("span",null,null)],512)}});const $p="Google Inc."===navigator.vendor;const Ap=be(!0),Pp=[];let Ip=0,Mp=!1;const Rp=e=>Pp.forEach((t=>t.userAction=e));function Bp(e={userAction:!1}){if(!Mp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Ip&&Rp(!0),Ip++,setTimeout((()=>{!--Ip&&Rp(!1)}),0)}),Ap)})),Mp=!0}Pp.push(e)}function Dp(){const e=qt({userAction:!1});return No((()=>{Bp(e)})),Ho((()=>{!function(e){const t=Pp.indexOf(e);t>=0&&Pp.splice(t,1)}(e)})),{state:e}}function jp(){const e=qt({attrs:{}});return No((()=>{let t=mi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Np(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function Vp(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Fp=["none","text","decimal","numeric","tel","search","email","url"],Hp=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Fp.indexOf(e)},cursorColor:{type:String,default:""}},hp),qp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function zp(e,t,n,o){let r=null;r=Ce((n=>{t.value=Vp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),eo((()=>e.modelValue),r),eo((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return jo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Wp(e,t){Dp();const n=ki((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}eo((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),No((()=>{n.value&&Cn(o)}))}function Up(e,t,n,o){$l(yc(),"getSelectedTextRange",Np);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=sn(null),r=iu(t,n),i=ki((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=ki((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=ki((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=ki((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=Vp(e.modelValue,e.type)||Vp(e.value,e.type);const u=qt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return eo((()=>u.focus),(e=>n("update:focus",e))),eo((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=zp(e,i,n,s);Wp(e,r),mp(0,r);const{state:l}=jp();!function(e,t){const n=br(cu,!1);if(!n)return;const o=mi(),r={submit(){const n=o.proxy;return[n[e],v(t)?n[t]:t.value]},reset(){v(t)?o.proxy[t]="":t.value=""}};n.addField(r),Ho((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}eo([()=>t.selectionStart,()=>t.selectionEnd],s),eo((()=>t.cursor),a),eo((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),g(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Yp=c({},Hp,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Xp=ie((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function Kp(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&Xp()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Gp=nu({name:"Input",props:Yp,emits:["confirm",...qp],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=ki((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=ki((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf($(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=sn(null!=t?t.toLocaleString():"");return eo((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),eo((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return sn("")}(e,i),l={fn:null};const c=sn(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:p,trigger:h}=Up(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=Kp(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=Kp(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));eo((()=>d.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const m=["number","digit"],g=ki((()=>m.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&p?oi("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):oi("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return oi("uni-input",{ref:c},[oi("div",{class:"uni-input-wrapper"},[io(oi("div",ui(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Xi,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?oi("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});function Zp(e){const t=[];return p(e)&&e.forEach((e=>{Zr(e)?e.type===Nr?t.push(...Zp(e.children)):t.push(e):p(e)&&t.push(...Zp(e))})),t}const Jp=function(e,t,n,o){e.addEventListener(t,(e=>{g(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Qp,eh;function th(e,t,n){Ho((()=>{document.removeEventListener("mousemove",Qp),document.removeEventListener("mouseup",eh)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Jp(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Jp(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Jp(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=Qp=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Jp(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=eh=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Jp(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}const nh=nu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return p(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=sn(null),r=sn(null),i=iu(o,n),s=function(e){const t=qt([...e.value]),n=qt({value:t,height:34});return eo((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=sn(null);No((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=sn([]),c=sn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==Fr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return yr("getPickerViewColumn",(function(e){return ki({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),yr("pickerViewProps",e),yr("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Zp(e);l.value=t,Cn((()=>{c.value=t}))}return oi("uni-picker-view",{ref:o},[oi(cp,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),oi("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class oh{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function rh(e,t,n){return e>t-n&&e<t+n}function ih(e,t){return rh(e,0,t)}class sh{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!ih(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(ih(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),ih(t,.4)&&(t=0),ih(o,.4)&&(o=0),o+=this._endPosition),this._solution&&ih(o-e,.4)&&ih(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),rh(this.x(),this._endPosition,.4)&&ih(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class ah{constructor(e,t,n){this._extent=e,this._friction=t||new oh(.01),this._spring=n||new sh(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class lh{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new ah(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(g(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const ch=nu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=sn(null),r=sn(null),i=br("getPickerViewColumn"),s=mi(),a=i?i(s):sn(0),l=br("pickerViewProps"),c=br("pickerViewState"),u=sn(34),d=sn(null);No((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const f=ki((()=>(c.height-u.value)/2)),{state:p}=jp();let h;const m=qt({current:a.value,length:0});let g;function v(){h&&!g&&(g=!0,Cn((()=>{g=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),h.update(e*u.value,void 0,u.value)})))}eo((()=>a.value),(e=>{e!==m.current&&(m.current=e,v())})),eo((()=>m.current),(e=>a.value=e)),eo([()=>u.value,()=>m.length,()=>c.height],v);let y=0;function b(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),h.scrollTo(e*u.value)}else y=t;e.preventDefault()}function _({clientY:e}){const t=o.value;if(!h.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),h.scrollTo(r*u.value)}}}const w=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new lh(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new oh(1e-4),spring:new sh(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});h=n,th(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()};return No(w),()=>{const e=t.default&&t.default();m.length=Zp(e).length;const n=`${f.value}px 0`;return oi("uni-picker-view-column",{ref:o},[oi("div",{onWheel:b,onClick:_,class:"uni-picker-view-group"},[oi("div",ui(p.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${f.value}px;${l.maskStyle}`}),null,16),oi("div",ui(p.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[oi(cp,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),oi("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),uh={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},dh={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const fh=(e,t,n)=>!n||p(n)&&!n.length?[]:n.map((n=>{var o;if(S(n)){if(!f(n,"type")||"node"===n.type){let r={[e]:""};const i=null==(o=n.name)?void 0:o.toLowerCase();if(!f(uh,i))return;return function(e,t){if(S(t))for(const n in t)if(f(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Hf(o))}}(i,n.attrs),r=c(r,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Ei(n.name,r,fh(e,t,n.children))}return"text"===n.type&&v(n.text)&&""!==n.text?ii((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return f(dh,t)&&dh[t]?dh[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function ph(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,r,i=[],s=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&Tp[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(vp))&&(e=e.substring(r[0].length),r[0].replace(vp,c),o=!1):0==e.indexOf("<")&&(r=e.match(gp))&&(e=e.substring(r[0].length),r[0].replace(gp,l),o=!1),o){var a=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(a)}if(e==s)throw"Parse Error: "+e;s=e}function l(e,n,o,r){if(n=n.toLowerCase(),_p[n])for(;i.last()&&wp[i.last()];)c("",i.last());if(xp[n]&&i.last()==n&&c("",n),(r=bp[n]||!!r)||i.push(n),t.start){var s=[];o.replace(yp,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Sp[t]?t:"";s.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,s,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o&&(o.children||(o.children=[]),o.children.push(n))}}),n.children}const hh=nu({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["itemclick"],setup(e,{emit:t}){const n=mi(),o=n&&n.vnode.scopeId||"",r=sn(null),i=sn([]),s=iu(r,t);function a(e,t={}){s("itemclick",e,t)}return eo((()=>e.nodes),(function(){let t=e.nodes;v(t)&&(t=ph(e.nodes)),i.value=fh(o,a,t)}),{immediate:!0}),()=>Ei("uni-rich-text",{ref:r},Ei("div",{},i.value))}}),mh=nu({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=sn(null),o=ki((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=ki((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return oi("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?oi("div",{class:"uni-scroll-view-refresh"},[oi("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?oi("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[oi("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),oi("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?oi("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[oi("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?oi("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),gh=be(!0),vh=nu({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=sn(null),i=sn(null),s=sn(null),a=sn(null),l=iu(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=ki((()=>Number(e.scrollTop)||0)),n=ki((()=>Number(e.scrollLeft)||0));return{state:qt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p,_scrollLeftChanged:h,_scrollTopChanged:m}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,f=()=>{};const p=ki((()=>e.scrollX)),h=ki((()=>e.scrollY)),m=ki((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),g=ki((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>x(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=m.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+g.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),p.value&&(n.scrollLeft<=m.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+g.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function _(t){p.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(p.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=p.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",f),a.value.removeEventListener("webkitTransitionEnd",f)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:C.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:C.y-T.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:C.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},C={x:0,y:e.refresherThreshold};return No((()=>{Cn((()=>{b(n.value),_(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(p.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(h.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},f=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},m=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},C={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",f,gh),s.value.addEventListener("touchmove",l,be(!1)),s.value.addEventListener("scroll",i,be(!1)),s.value.addEventListener("touchend",m,gh),Ho((()=>{s.value.removeEventListener("touchstart",f),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",m)}))})),Oo((()=>{h.value&&(s.value.scrollTop=t.lastScrollTop),p.value&&(s.value.scrollLeft=t.lastScrollLeft)})),eo(n,(e=>{b(e)})),eo(o,(e=>{_(e)})),eo((()=>e.scrollIntoView),(e=>{w(e)})),eo((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:p,realScrollY:h,_scrollTopChanged:b,_scrollLeftChanged:_}}(e,c,u,d,l,r,i,a,t),g=ki((()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=ki((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:f}=c;return oi("uni-scroll-view",{ref:r},[oi("div",{ref:s,class:"uni-scroll-view"},[oi("div",{ref:i,style:g.value,class:v.value},[t?oi(mh,{refreshState:f,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,oi("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function yh(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,d=0,f=1,p=null,h=!1,m=0,g="";const v=ki((()=>n.value.length>t.displayMultipleItems)),y=ki((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),d=Math.max(o-(l+1),l-s,0),f=Math.max(o-(c+1),c-s,0),p=Math.min(u,d,f),h=[n,l,c][[u,d,f].indexOf(p)];t.updatePosition(h,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*f+"%")+", "+(e.vertical?100*-r*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),d=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){p=null}function x(){if(!p)return void(h=!1);const e=p,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),p=null,h=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){w();const i=t.duration,s=n.value.length;let a=d;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);p={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,g="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function C(e){e?T():s()}return eo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(g="",t.current=o)})),eo([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),p&&(b(p.toPos),p=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);f=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const a=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-m),m=l):(b(l),e.autoplay&&T())):(u=!0,b(-t.displayMultipleItems-1))})),eo((()=>t.interval),(()=>{c&&(s(),T())})),eo((()=>t.current),((e,o)=>{!function(e,o){const r=g;g="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),eo((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),eo((()=>e.autoplay&&!t.userTracking),C),C(e.autoplay&&!t.userTracking),No((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(m):(g="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}th(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),m=d,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=m+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=a-s||1,f=o.value;e.vertical?u(-r.dy/f.offsetHeight,-r.ddy/d):u(-r.dx/f.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),qo((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,g="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const bh=nu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=sn(null),r=iu(o,n),i=sn(null),s=sn(null),a=function(e){return qt({interval:ki((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:ki((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:ki((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=ki((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:lc(e.previousMargin,!0),bottom:lc(e.nextMargin,!0)}:{top:0,bottom:0,left:lc(e.previousMargin,!0),right:lc(e.nextMargin,!0)}),t})),c=ki((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],f=sn([]);function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Jt(o))}f.value=e}yr("addSwiperContext",(function(e){d.push(e),p()}));yr("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())}));const{onSwiperDotClick:h,circularEnabled:m,swiperEnabled:g}=yh(e,a,f,s,n,r);let v=()=>null;return v=_h(o,e,a,h,f,m,g),()=>{const n=t.default&&t.default();return u=Zp(n),oi("uni-swiper",{ref:o},[oi("div",{ref:i,class:"uni-swiper-wrapper"},[oi("div",{class:"uni-swiper-slides",style:l.value},[oi("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&oi("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,o)=>oi("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),_h=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,u=!1,d=sn(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Jn((()=>{a="auto"===t.navigation,d.value=!0!==t.navigation||a,b()})),Jn((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,u=!0,a&&(d.value=!0))}));const p={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function h(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const m=()=>hc(pc,t.navigationColor,26);let g;const v=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(r-a<u/3||l-r<u/3):!(o-i<c/3||s-o<c/3),f)return g=setTimeout((()=>{d.value=f}),300);d.value=f},y=()=>{d.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return No(b),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?oi(Nr,null,[oi("div",ui({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},p),[m()],16,["onClick"]),oi("div",ui({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>h(e,"next",u)},p),[m()],16,["onClick"])]):null}},wh=nu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=sn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return No((()=>{const e=br("addSwiperContext");e&&e(o)})),qo((()=>{const e=br("removeSwiperContext");e&&e(o)})),()=>oi("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),xh={ensp:" ",emsp:" ",nbsp:" "};function Sh(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&xh[t]&&" "===i&&(i=xh[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,xh.nbsp).replace(/&ensp;/g,xh.ensp).replace(/&emsp;/g,xh.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const Th=nu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Fr){const n=Sh(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ii(e)),t!==r&&o.push(oi("br"))}))}else o.push(t)})),oi("uni-text",{ref:n,selectable:!!e.selectable||null},[oi("span",null,o)],8,["selectable"])}}}),Ch=nu({name:"View",props:c({},su),setup(e,{slots:t}){const n=sn(null),{hovering:o,binding:r}=au(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?oi("uni-view",ui({class:o.value?i:"",ref:n},r),[Ko(t,"default")],16):oi("uni-view",{ref:n},[Ko(t,"default")],512)}}});function kh(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Eh(e,t,n){e&&$l(n||yc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function Oh(e,t){e&&function(e,t){t=Ll(e,t),delete Ol[t]}(t||yc(),e)}function Lh(e,t,n,o){const r=mi().proxy;No((()=>{Eh(t||kh(r),e,o),!n&&t||eo((()=>r.id),((t,n)=>{Eh(kh(r,t),e,o),Oh(n&&kh(r,n))}))})),Ho((()=>{Oh(t||kh(r),o)}))}function $h(e,t,n,o){g(t)&&Bo(e,t.bind(n),o)}function Ah(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&("page"!==o||"component"!==t.renderer)&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!g(t))&&(Oe.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];p(r)?r.forEach((e=>$h(o,e,n,t))):$h(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,Sc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&Sc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function Ph(e,t,n){Ah(e,t,n)}function Ih(e,t,n){return e[t]=n}function Mh(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Rh(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?Sc(i.proxy,"onError",n):mn(n,0,o&&o.$.vnode,!1)}}function Bh(e,t){return e?[...new Set([].concat(e,t))]:t}function Dh(e){const t=e.config;var n;t.errorHandler=$e(e,Rh),n=t.optionMergeStrategies,Oe.forEach((e=>{n[e]=Bh}));const o=t.globalProperties;o.$set=Ih,o.$applyOptions=Ph,o.$callMethod=Mh,function(e){Le.forEach((t=>t(e)))}(e)}function jh(e){const t=el({history:Fh(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:Vh});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(Nh[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let Nh=Object.create(null);const Vh=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,Nh[o]);if(t)return t}return{left:0,top:0};var o};function Fh(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),ha(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=kf(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=yf(t[r]);Ef(Af(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const Hh={install(e){Dh(e),Vc(e),Zc(e),e.config.warnHandler||(e.config.warnHandler=qh),jh(e)}};function qh(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const zh={class:"uni-async-loading"},Wh=oi("i",{class:"uni-loading"},null,-1),Uh=ou({name:"AsyncLoading",render:()=>(Wr(),Gr("div",zh,[Wh]))});function Yh(){window.location.reload()}const Xh=ou({name:"AsyncError",props:["error"],setup(){bl();const{t:e}=vl();return()=>oi("div",{class:"uni-async-error",onClick:Yh},[e("uni.async.error")],8,["onClick"])}});let Kh;function Gh(){return Kh}function Zh(e){Kh=e,Object.defineProperty(Kh.$.ctx,"$children",{get:()=>kf().map((e=>e.$vm))});const t=Kh.$.appContext.app;t.component(Uh.name)||t.component(Uh.name,Uh),t.component(Xh.name)||t.component(Xh.name,Xh),function(e){e.$vm=e,e.$mpType="app";const t=sn(vl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Kh),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Kh),Kc(),$c()}function Jh(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=mi();if(n(r.proxy),o(r),i)return i(e,t)},e}function Qh(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Jh(e.default,t):Jh(e,t)}function em(e){return Qh(e,{clone:!0,init:$f,setup(e){e.$pageInstance=e;const t=_u(),n=xe(t.query);e.attrs.__pageQuery=n,yf(e.proxy).options=n,e.proxy.options=n;const o=yu();var r,i;return mf(o),e.onReachBottom=qt([]),e.onPageScroll=qt([]),eo([e.onReachBottom,e.onPageScroll],(()=>{const t=gc();e.proxy===t&&Vf(e,o)}),{once:!0}),jo((()=>{Mf(e,o)})),No((()=>{Rf(e);const{onReady:n}=e;n&&M(n),rm(t)})),$o((()=>{if(!e.__isVisible){Mf(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&M(n),Cn((()=>{rm(t)}))}}),"ba",r),function(e,t){$o(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&M(t)}}})),i=o.id,jg.subscribe(Ll(i,"invokeViewApi"),Al),Ho((()=>{!function(e){jg.unsubscribe(Ll(e,"invokeViewApi")),Object.keys(Ol).forEach((t=>{0===t.indexOf(e+".")&&delete Ol[t]}))}(o.id)})),n}})}function tm(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=gm(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Ng.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function nm(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Ng.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function om(){const{emit:e}=Ng;"visible"===document.visibilityState?e("onAppEnterForeground",c({},lp)):e("onAppEnterBackground")}function rm(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Sc("onTabItemTap",{index:n,text:t,pagePath:o})}const im=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let sm;function am(){if(sm=sm||im.__DC_STAT_UUID,!sm){sm=Date.now()+""+Math.floor(1e7*Math.random());try{im.__DC_STAT_UUID=sm}catch(e){}}return sm}function lm(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function cm(){let e,t="0",n="",o="phone";const r=navigator.language;if(Wf){e="iOS";const o=qf.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=qf.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(zf){e="Android";const o=qf.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=qf.match(/\((.+?)\)/),i=r?r[1].split(";"):qf.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Kf){if(n="iPad",e="iOS",o="pad",t=g(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=qf.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Uf||Yf||Xf){n="PC",e="PC",o="pc",t="0";let r=qf.match(/\((.+?)\)/)[1];if(Uf){switch(e="Windows",Uf[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Yf){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Xf){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(qf)&&(a=t[n],l=qf.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLowerCase(),browserVersion:l,language:r,deviceType:o,ua:qf,osname:e,osversion:t,theme:lm()}}const um=Zu(0,(()=>{const e=window.devicePixelRatio,t=Gf(),n=Zf(t),o=Jf(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Qf(o);let s=window.innerHeight;const a=Jl.top,l={left:Jl.left,right:i-Jl.right,top:Jl.top,bottom:s-Jl.bottom,width:i-Jl.left-Jl.right,height:s-Jl.top-Jl.bottom},{top:c,bottom:u}=oc();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Jl.top,right:Jl.right,bottom:Jl.bottom,left:Jl.left},screenTop:r-s}}));let dm,fm=!0;function pm(){fm&&(dm=cm())}const hm=Zu(0,(()=>{pm();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:u}=dm;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:am(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLowerCase():void 0,osVersion:u})})),mm=Zu(0,(()=>{pm();const{theme:e,language:t,browserName:n,browserVersion:o}=dm;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Od?Od():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),gm=Zu(0,(()=>{fm=!0,pm(),fm=!1;const e=um(),t=hm(),n=mm();fm=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=dm,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),vm=!!window.navigator.vibrate,ym=Ju("vibrateShort",((e,{resolve:t,reject:n})=>{vm&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")}));const bm=Ju("setClipboardData",((e,t)=>{return n=void 0,o=[e,t],r=function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.setAttribute("inputmode","none"),r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}},new Promise(((e,t)=>{var i=e=>{try{a(r.next(e))}catch(n){t(n)}},s=e=>{try{a(r.throw(e))}catch(n){t(n)}},a=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,s);a((r=r.apply(n,o)).next())}));var n,o,r}),0,$d);const _m=Zu(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function wm(e){const t=localStorage&&localStorage.getItem(e);if(!v(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=v(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const xm=Zu(0,(e=>{try{return wm(e)}catch(t){return""}})),Sm=Zu(0,(e=>{localStorage&&localStorage.removeItem(e)})),Tm=Zu(0,(()=>{localStorage&&localStorage.clear()})),Cm=Ju("clearStorage",((e,{resolve:t})=>{Tm(),t()})),km={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Em({count:e,sourceType:t,type:n,extension:o}){Bp();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${km[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let Om=null;const Lm=Ju("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{Sl();const{t:i}=vl();Om&&(document.body.removeChild(Om),Om=null),Om=Em({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Om),Om.addEventListener("cancel",(()=>{r("chooseImage:fail cancel")})),Om.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||sp(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),Om.click(),Ip||console.warn(i("uni.chooseFile.notUserActivation"))}),0,Ad),$m={esc:["Esc","Escape"],enter:["Enter"]},Am=Object.keys($m);function Pm(){const e=sn(""),t=sn(!1),n=n=>{if(t.value)return;const o=Am.find((e=>-1!==$m[e].indexOf(n.key)));o&&(e.value=o),Cn((()=>e.value=""))};return No((()=>{document.addEventListener("keyup",n)})),Ho((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const Im=oi("div",{class:"uni-mask"},null,-1);function Mm(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Ss(bo({setup:()=>()=>(Wr(),Gr(e,t,null,16))}))}function Rm(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Bm(e,{onEsc:t,onEnter:n}){const o=sn(e.visible),{key:r,disable:i}=Pm();return eo((()=>e.visible),(e=>o.value=e)),eo((()=>o.value),(e=>i.value=!e)),Jn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}const Dm=Gu("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,enableChunked:s,withCredentials:a,timeout:l=__uniConfig.networkTimeout.request},{resolve:c,reject:u})=>{let d=null;const p=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)d=t;else if("json"===p)try{d=JSON.stringify(t)}catch(m){d=t.toString()}else if("urlencoded"===p){const e=[];for(const n in t)f(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));d=e.join("&")}else d=t.toString();let h;if(s){if(void 0===typeof window.fetch||void 0===typeof window.AbortController)throw new Error("fetch or AbortController is not supported in this environment");const t=new AbortController,s=t.signal;h=new Nm(t);const f={method:o,headers:n,body:d,signal:s,credentials:a?"include":"same-origin"},p=setTimeout((function(){h.abort(),u("timeout",{errCode:5})}),l);f.signal.addEventListener("abort",(function(){clearTimeout(p),u("abort",{errCode:600003})})),window.fetch(e,f).then((e=>{const t=e.status,n=e.headers,o=e.body,s={};n.forEach(((e,t)=>{s[t]=e}));const a=jm(s);if(h._emitter.emit("headersReceived",{header:s,statusCode:t,cookies:a}),!o)return void c({data:"",statusCode:t,header:s,cookies:a});const l=o.getReader(),u=[],d=()=>{l.read().then((({done:e,value:n})=>{if(e){const e=function(e){const t=e.reduce(((e,t)=>e+t.byteLength),0),n=new Uint8Array(t);let o=0;for(const r of e)n.set(new Uint8Array(r),o),o+=r.byteLength;return n.buffer}(u);let n="text"===i?(new TextDecoder).decode(e):e;return"text"===i&&(n=Fm(n,i,r)),void c({data:n,statusCode:t,header:s,cookies:a})}const o=n;u.push(o),h._emitter.emit("chunkReceived",{data:o}),d()}))};d()}),(e=>{u(e,{errCode:5})}))}else{const t=new XMLHttpRequest;h=new Nm(t),t.open(o,e);for(const e in n)f(n,e)&&t.setRequestHeader(e,n[e]);const s=setTimeout((function(){t.onload=t.onabort=t.onerror=null,h.abort(),u("timeout",{errCode:5})}),l);t.responseType=i,t.onload=function(){clearTimeout(s);const e=t.status;let n="text"===i?t.responseText:t.response;"text"===i&&(n=Fm(n,i,r)),c({data:n,statusCode:e,header:Vm(t.getAllResponseHeaders()),cookies:[]})},t.onabort=function(){clearTimeout(s),u("abort",{errCode:600003})},t.onerror=function(){clearTimeout(s),u(void 0,{errCode:5})},t.withCredentials=a,t.send(d)}return h}),0,Rd),jm=e=>{let t=e["Set-Cookie"]||e["set-cookie"],n=[];if(!t)return[];"["===t[0]&&"]"===t[t.length-1]&&(t=t.slice(1,-1));const o=t.split(";");for(let r=0;r<o.length;r++)-1!==o[r].indexOf("Expires=")||-1!==o[r].indexOf("expires=")?n.push(o[r].replace(",","")):n.push(o[r]);return n=n.join(";").split(","),n};class Nm{constructor(e){this._requestOnChunkReceiveCallbackId=0,this._requestOnChunkReceiveCallbacks=new Map,this._requestOnHeadersReceiveCallbackId=0,this._requestOnHeadersReceiveCallbacks=new Map,this._emitter=new Pe,this._controller=e}abort(){this._controller&&(this._controller.abort(),delete this._controller)}onHeadersReceived(e){return this._emitter.on("headersReceived",e),this._requestOnHeadersReceiveCallbackId++,this._requestOnHeadersReceiveCallbacks.set(this._requestOnHeadersReceiveCallbackId,e),this._requestOnHeadersReceiveCallbackId}offHeadersReceived(e){if(null==e)return void this._emitter.off("headersReceived");if("function"==typeof e)return void this._requestOnHeadersReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnHeadersReceiveCallbacks.delete(n),this._emitter.off("headersReceived",e))}));const t=this._requestOnHeadersReceiveCallbacks.get(e);t&&(this._requestOnHeadersReceiveCallbacks.delete(e),this._emitter.off("headersReceived",t))}onChunkReceived(e){return this._emitter.on("chunkReceived",e),this._requestOnChunkReceiveCallbackId++,this._requestOnChunkReceiveCallbacks.set(this._requestOnChunkReceiveCallbackId,e),this._requestOnChunkReceiveCallbackId}offChunkReceived(e){if(null==e)return void this._emitter.off("chunkReceived");if("function"==typeof e)return void this._requestOnChunkReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnChunkReceiveCallbacks.delete(n),this._emitter.off("chunkReceived",e))}));const t=this._requestOnChunkReceiveCallbacks.get(e);t&&(this._requestOnChunkReceiveCallbacks.delete(e),this._emitter.off("chunkReceived",t))}}function Vm(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}function Fm(e,t,n){let o=e;if("text"===t&&"json"===n)try{o=JSON.parse(o)}catch(r){}return o}class Hm{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){g(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const qm=Gu("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Hm;return p(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(ip(e)):rp(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(s).forEach((e=>{d.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Bd),zm=Ju("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Sc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(Gh().$router.go(-e.delta),t()):n("onBackPress")}),0,Fd),Wm=Ju("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(bf.handledBeforeEntryPageRoutes)return sf({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);_f.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,jd);function Um(e){__uniConfig.darkmode&&Ng.on("onThemeChange",e)}function Ym(e){Ng.off("onThemeChange",e)}function Xm(e){let t={};return __uniConfig.darkmode&&(t=Re(e,__uniConfig.themeConfig,lm())),__uniConfig.darkmode?t:e}function Km(e,t){const n=Yt(e),o=n?qt(Xm(e)):Xm(e);return __uniConfig.darkmode&&n&&eo(e,(e=>{const t=Xm(e);for(const n in t)o[n]=t[n]})),t&&Um(t),o}const Gm={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Zm=bo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=sn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=Bm(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=sn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Gm[e].cancelColor})(e,t)};return Jn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===lm()&&n({theme:"dark"}),Um(n))):Ym(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,oi(Mi,{name:"uni-fade"},{default:()=>[io(oi("uni-modal",{onTouchmove:Ql},[Im,oi("div",{class:"uni-modal"},[t?oi("div",{class:"uni-modal__hd"},[oi("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?oi("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):oi("div",{class:"uni-modal__bd",onTouchmovePassive:ec,textContent:o},null,40,["onTouchmovePassive","textContent"]),oi("div",{class:"uni-modal__ft"},[l&&oi("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),oi("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Xi,s.value]])]})}}});let Jm;const Qm=ie((()=>{Ng.on("onHidePopup",(()=>Jm.visible=!1))}));let eg;function tg(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&Jm.editable&&(o.content=t),eg&&eg(o)}const ng=Ju("showModal",((e,{resolve:t})=>{Qm(),eg=t,Jm?(c(Jm,e),Jm.visible=!0):(Jm=qt(e),Cn((()=>(Mm(Zm,Jm,tg).mount(Rm("u-a-m")),Cn((()=>Jm.visible=!0))))))}),0,Xd),og={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Kd.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},rg={light:"#fff",dark:"rgba(255,255,255,0.9)"},ig=e=>rg[e],sg=bo({name:"Toast",props:og,setup(e){_l(),wl();const{Icon:t}=function(e){const t=sn(ig(lm())),n=({theme:e})=>t.value=ig(e);Jn((()=>{e.visible?Um(n):Ym(n)}));return{Icon:ki((()=>{switch(e.icon){case"success":return oi(hc(dc,t.value,38),{class:"uni-toast__icon"});case"error":return oi(hc(fc,t.value,38),{class:"uni-toast__icon"});case"loading":return oi("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=Bm(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return oi(Mi,{name:"uni-fade"},{default:()=>[io(oi("uni-toast",{"data-duration":r},[o?oi("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Ql},null,40,["onTouchmove"]):"",s||t.value?oi("div",{class:"uni-toast"},[s?oi("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,oi("p",{class:"uni-toast__content"},[i])]):oi("div",{class:"uni-sample-toast"},[oi("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Xi,n.value]])]})}}});let ag,lg,cg="";const ug=Ne();function dg(e){ag?c(ag,e):(ag=qt(c(e,{visible:!1})),Cn((()=>{ug.run((()=>{eo([()=>ag.visible,()=>ag.duration],(([e,t])=>{if(e){if(lg&&clearTimeout(lg),"onShowLoading"===cg)return;lg=setTimeout((()=>{gg("onHideToast")}),t)}else lg&&clearTimeout(lg)}))})),Ng.on("onHidePopup",(()=>gg("onHidePopup"))),Mm(sg,ag,(()=>{})).mount(Rm("u-a-t"))}))),setTimeout((()=>{ag.visible=!0}),10)}const fg=Ju("showToast",((e,{resolve:t,reject:n})=>{dg(e),cg="onShowToast",t()}),0,Gd),pg={icon:"loading",duration:1e8,image:""},hg=Ju("showLoading",((e,{resolve:t,reject:n})=>{c(e,pg),dg(e),cg="onShowLoading",t()}),0,Yd),mg=Ju("hideLoading",((e,{resolve:t,reject:n})=>{gg("onHideLoading"),t()}));function gg(e){const{t:t}=vl();if(!cg)return;let n="";if("onHideToast"===e&&"onShowToast"!==cg?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==cg&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);cg="",setTimeout((()=>{ag.visible=!1}),10)}const vg=Ju("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Hf(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Hf(t.substring(4,t.length-1))}')`:Hf(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function yg(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Ng.emit("onNavigationBarChange",{titleText:t})}Jn(t),Oo(t)}const bg=Ju("stopPullDownRefresh",((e,{resolve:t})=>{Ng.invokeViewMethod("stopPullDownRefresh",{},yc()),t()})),_g=ou({name:"TabBar",setup(){const e=sn([]),t=cf(),n=Km(t,(()=>{const e=Xm(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}sn(c({type:"midButton"},e.midButton)),Jn(n)}(n,e),function(e){eo((()=>e.shown),(t=>{ic({"--window-bottom":gf(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Jn((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=re(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?ef({from:"tabBar",url:i,tabBarText:r}):Sc("onTabItemTap",{index:n,text:r,pagePath:o})}}(tl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=ki((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||pf&&n&&"none"!==n&&(t=wg[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=ki((()=>{const{borderStyle:t,borderColor:n}=e;return n&&v(n)?{backgroundColor:n}:{backgroundColor:xg[t]||xg.black}})),o=ki((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return No((()=>{n.iconfontSrc&&vg({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return oi("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[Sg(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return oi("uni-tabbar",{class:"uni-tabbar-"+n.position},[oi("div",{class:"uni-tabbar",style:r.value},[oi("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),oi("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const wg={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},xg={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Sg(e,t,n,o,r,i){const{height:s}=i;return oi("div",{class:"uni-tabbar__bd",style:{height:s}},[n?Cg(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&Tg(t,r,i),r.text&&kg(e,r,i),r.redDot&&Eg(r.badge)],4)}function Tg(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return oi("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&oi("img",{src:Hf(e)},null,8,["src"])],6)}function Cg(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return oi("div",{class:l,style:c},["midButton"!==i&&oi("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function kg(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return oi("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function Eg(e){return oi("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Og=ou({name:"Layout",setup(e,{emit:t}){const n=sn(null);rc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=tl();return{routeKey:ki((()=>Af("/"+e.meta.route,wu()))),isTabBar:ki((()=>e.meta.isTabBar)),routeCache:If}}(),{layoutState:r,windowState:i}=function(){_u();{const e=qt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return eo((()=>e.marginWidth),(e=>rc({"--window-margin":e+"px"}))),eo((()=>e.leftWindowWidth+e.marginWidth),(e=>{rc({"--window-left":e+"px"})})),eo((()=>e.rightWindowWidth+e.marginWidth),(e=>{rc({"--window-right":e+"px"})})),{layoutState:e,windowState:ki((()=>({})))}}}();!function(e,t){const n=_u();function o(){const o=document.body.clientWidth,r=kf();let i={};if(r.length>0){i=yf(r[r.length-1]).meta}else{const e=Lc(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((f(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,Cn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,Cn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}eo([()=>n.path],o),No((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=_u(),n=cf(),o=ki((()=>t.meta.isTabBar&&n.shown));return rc({"--tab-bar-height":n.height}),o}(),a=function(e){const t=sn(!1);return ki((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return oi(Qa,null,{default:Vn((({Component:o})=>[(Wr(),Gr(ko,{matchBy:"key",cache:n},[(Wr(),Gr(Yn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return io(oi(_g,null,null,512),[[Xi,e.value]])}(s);return oi("uni-app",{ref:n,class:a.value},[e,t],2)}}});function Lg(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Zr(e)}function $g(e){if(e.mode===Ig.TIME)return"00:00";if(e.mode===Ig.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case Mg.YEAR:return t.toString();case Mg.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Ag(e){if(e.mode===Ig.TIME)return"23:59";if(e.mode===Ig.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case Mg.YEAR:return t.toString();case Mg.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Pg(e,t,n,o){const r=e.mode===Ig.DATE?"-":":",i=e.mode===Ig.DATE?t.dateArray:t.timeArray;let s;if(e.mode===Ig.TIME)s=2;else switch(e.fields){case Mg.YEAR:s=1;break;case Mg.MONTH:s=2;break;default:s=3}const a=String(n).split(r);let l=[];for(let c=0;c<s;c++){const e=a[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?Pg(e,t,o):l.map((()=>0))),l}const Ig={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Mg={YEAR:"year",MONTH:"month",DAY:"day"},Rg={PICKER:"picker",SELECT:"select"},Bg=nu({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Ig.SELECTOR,validator:e=>Object.values(Ig).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>$g(e)},end:{type:String,default:e=>Ag(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Cl();const{t:o}=vl(),r=sn(null),i=sn(null),s=sn(null),a=sn(null),l=sn(!1),{state:u,rangeArray:d}=function(e){const t=qt({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=ki((()=>{let n=e.range;switch(e.mode){case Ig.SELECTOR:return[n];case Ig.MULTISELECTOR:return n;case Ig.TIME:return t.timeArray;case Ig.DATE:{const n=t.dateArray;switch(e.fields){case Mg.YEAR:return[n[0]];case Mg.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),f=iu(r,t),{system:h,selectorTypeComputed:m,_show:g,_l10nColumn:v,_l10nItem:y,_input:b,_fixInputPosition:_,_pickerViewChange:w,_cancel:x,_change:S,_resetFormData:T,_getFormData:C,_createTime:k,_createDate:E,_setValueSync:O}=function(e,t,n,o,r,i,s){const a=function(){const e=sn(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=sn("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=ki((()=>{const t=e.selectorType;return Object.values(Rg).includes(t)?t:a.value?Rg.PICKER:Rg.SELECT})),u=ki((()=>e.mode===Ig.DATE&&!Object.values(Mg).includes(e.fields)&&t.isDesktop?l.value:"")),d=ki((()=>Pg(e,t,e.start,$g(e)))),f=ki((()=>Pg(e,t,e.end,Ag(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const s=i.getBoundingClientRect();t.popover={top:s.top,left:s.left,width:s.width,height:s.height},setTimeout((()=>{t.visible=!0}),20)}function m(){return{value:t.valueSync,key:e.name}}function g(){switch(e.mode){case Ig.SELECTOR:t.valueSync=0;break;case Ig.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case Ig.DATE:case Ig.TIME:t.valueSync=""}}function v(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function y(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function b(){let e=[];const n=y();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function _(e){return 60*e[0]+e[1]}function w(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function x(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function S(){let n=e.value;switch(e.mode){case Ig.MULTISELECTOR:{p(n)||(n=t.valueArray),p(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),s=isNaN(o)?isNaN(i)?0:i:o,a=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,s<0||s>a?0:s)}}break;case Ig.TIME:case Ig.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function T(){let n,o=t.valueSync;switch(e.mode){case Ig.MULTISELECTOR:n=[...o];break;case Ig.TIME:n=Pg(e,t,o,ae({mode:Ig.TIME}));break;case Ig.DATE:n=Pg(e,t,o,ae({mode:Ig.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function C(){let n=t.valueArray;switch(e.mode){case Ig.SELECTOR:return n[0];case Ig.MULTISELECTOR:return n.map((e=>e));case Ig.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case Ig.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function k(){O(),t.valueChangeSource="click";const e=C();t.valueSync=p(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:s,pageY:a}=e;if(s>o&&s<o+r&&a>n&&a<n+i)return}O(),n("cancel",{},{})}function O(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function L(){e.mode===Ig.SELECTOR&&c.value===Rg.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function $(e){const n=e.target;t.valueSync=n.value,Cn((()=>{k()}))}function A(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;s.value.style.left=e.clientX-t.left-1.5*n+"px",s.value.style.top=e.clientY-t.top-.5*n+"px"}}function P(e){t.valueArray=I(e.detail.value,!0)}function I(t,n){const{getLocale:o}=vl();if(e.mode===Ig.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case Mg.YEAR:return t;case Mg.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function M(t,n){const{getLocale:o}=vl();if(e.mode===Ig.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==Mg.YEAR&&n===(e.fields===Mg.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return eo((()=>t.visible),(e=>{e?(clearTimeout(Dg),t.contentVisible=e,L()):Dg=setTimeout((()=>{t.contentVisible=e}),300)})),eo([()=>e.mode,()=>e.value,()=>e.range],S,{deep:!0}),eo((()=>t.valueSync),T,{deep:!0}),eo((()=>t.valueArray),(o=>{if(e.mode===Ig.TIME||e.mode===Ig.DATE){const n=e.mode===Ig.TIME?_:w,o=t.valueArray,r=d.value,i=f.value;if(e.mode===Ig.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?x(o,r):n(o)>n(i)&&x(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===Ig.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:E,_change:k,_l10nColumn:I,_l10nItem:M,_input:$,_resetFormData:g,_getFormData:m,_createTime:v,_createDate:b,_setValueSync:S,_fixInputPosition:A,_pickerViewChange:P}}(e,u,f,r,i,s,a);!function(e,t,n){const{key:o,disable:r}=Pm();Jn((()=>{r.value=!e.visible})),eo(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(u,x,S),function(e,t){const n=br(cu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),Ho((()=>{n.removeField(o)}))}}(T,C),k(),E(),O();const L=function(e){const t=sn(0),n=sn(0),o=ki((()=>t.value>=500&&n.value>=500)),r=ki((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,s=e.popover;function a(e){return Number(e)||0}if(o.value&&s){c(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=a(s.left),t=a(s.width?s.width:300),o=a(s.top),l=a(s.height),u=e+t/2;r.transform="none !important";const d=Math.max(0,u-t/2);r.left=`${d}px`,s.width&&(r.width=`${t}px`);let f=Math.max(12,u-d);f=Math.min(t-12,f),i.left=`${f}px`;const p=n.value/2;o+l-p>p-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return No((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=gm();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),qo((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}(u);return Jn((()=>{u.isDesktop=L.isDesktop.value,u.popupStyle=L.popupStyle.value})),Ho((()=>{i.value&&i.value.remove()})),No((()=>{l.value=!0})),()=>{let t;const{visible:c,contentVisible:f,valueArray:p,popupStyle:T,valueSync:C}=u,{rangeKey:k,mode:E,start:O,end:L}=e,$=lu(e,"disabled");return oi("uni-picker",ui({ref:r},$,{onClick:ru(g)}),[l.value?oi("div",{ref:i,class:["uni-picker-container",`uni-${E}-${m.value}`],onWheel:Ql,onTouchmove:Ql},[oi(Mi,{name:"uni-fade"},{default:()=>[io(oi("div",{class:"uni-mask uni-picker-mask",onClick:ru(x),onMousemove:_},null,40,["onClick","onMousemove"]),[[Xi,c]])]}),h.value?null:oi("div",{class:[{"uni-picker-toggle":c},"uni-picker-custom"],style:T.content},[oi("div",{class:"uni-picker-header",onClick:ec},[oi("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:ru(x)},[o("uni.picker.cancel")],8,["onClick"]),oi("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:S},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),f?oi(nh,{value:v(p),class:"uni-picker-content",onChange:w},Lg(t=Xo(v(d.value),((e,t)=>{let n;return oi(ch,{key:t},Lg(n=Xo(e,((e,n)=>oi("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[k]||"":y(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,oi("div",{ref:s,class:"uni-picker-select",onWheel:ec,onTouchmove:ec},[Xo(d.value[0],((e,t)=>oi("div",{key:t,class:["uni-picker-item",{selected:p[0]===t}],onClick:()=>{p[0]=t,S()}},["object"==typeof e?e[k]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),oi("div",{style:T.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,oi("div",null,[n.default&&n.default()]),h.value?oi("div",{class:"uni-picker-system",onMousemove:ru(_)},[oi("input",{class:["uni-picker-system_input",h.value],ref:a,value:C,type:E,tabindex:"-1",min:O,max:L,onChange:e=>{b(e),ec(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Dg;const jg=c(Pl,{publishHandler(e,t,n){Ng.subscribeHandler(e,t,n)}}),Ng=c(qc,{publishHandler(e,t,n){jg.subscribeHandler(e,t,n)}}),Vg=ou({name:"PageHead",setup(){const e=sn(null),t=yu(),n=Km(t.navigationBar,(()=>{const e=Xm(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=ki((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=ki((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return oi("div",{class:"uni-page-head-btn",onClick:Hg},[hc(pc,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),s=n.type||"default",a="transparent"!==s&&"float"!==s&&oi("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return oi("uni-page-head",{"uni-page-head-type":s},[oi("div",{ref:e,class:o.value,style:r.value},[oi("div",{class:"uni-page-head-hd"},[i]),Fg(n),oi("div",{class:"uni-page-head-ft"},[])],6),a],8,["uni-page-head-type"])}}});function Fg(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return oi("div",{class:"uni-page-head-bd"},[oi("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?oi("i",{class:"uni-loading"},null):r?oi("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function Hg(){1===Cf().length?rf({url:"/"}):zm({from:"backbutton",success(){}})}const qg={name:"PageRefresh",setup(){const{pullToRefresh:e}=yu();return{offset:e.offset,color:e.color}}},zg=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Wg={class:"uni-page-refresh-inner"},Ug=["fill"],Yg=[ni("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),ni("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Xg={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Kg=["stroke"];const Gg=zg(qg,[["render",function(e,t,n,o,r,i){return Wr(),Kr("uni-page-refresh",null,[ni("div",{style:pe({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[ni("div",Wg,[(Wr(),Kr("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},Yg,8,Ug)),(Wr(),Kr("svg",Xg,[ni("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Kg)]))])],4)])}]]);function Zg(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const Jg="aborting",Qg="refreshing",ev="restoring";function tv(e){const t=yu(),{id:n,pullToRefresh:o}=t,{range:r,height:i}=o;let s,a,l,c,u,d,f,p;Lh((()=>{t.enablePullDownRefresh&&(p||(p=Qg,v(),setTimeout((()=>{x()}),50)))}),"startPullDownRefresh",!1,n),Lh((()=>{t.enablePullDownRefresh&&p===Qg&&(y(),p=ev,v(),function(e){if(!a)return;l.transition="-webkit-transform 0.3s",l.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),l.transition="",l.transform="translate3d(-50%, 0, 0)",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{y(),p=h=m=null})))}),"stopPullDownRefresh",!1,n),No((()=>{s=e.value.$el,a=s.querySelector(".uni-page-refresh"),l=a.style,c=a.querySelector(".uni-page-refresh-inner").style}));let h=null,m=null;function g(e){p&&s&&s.classList[e]("uni-page-refresh--"+p)}function v(){g("add")}function y(){g("remove")}const b=ru((e=>{if(!t.enablePullDownRefresh)return;const n=e.changedTouches[0];u=n.identifier,d=n.pageY,f=!([Jg,Qg,ev].indexOf(p)>=0)})),_=ru((e=>{if(!t.enablePullDownRefresh)return;if(!f)return;if(!Zg(e,u,d))return;let{deltaY:n}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(u=null);if(n<0&&!p)return;e.cancelable&&e.preventDefault(),null===h&&(m=n,p="pulling",v()),n-=m,n<0&&(n=0),h=n;(n>=r&&"reached"!==p||n<r&&"pulling"!==p)&&(y(),p="reached"===p?"pulling":"reached",v()),function(e){if(!a)return;let t=e/r;t>1?t=1:t*=t*t;const n=Math.round(e/(r/i))||0;c.transform="rotate("+360*t+"deg)",l.clip="rect("+(45-n)+"px,45px,45px,-5px)",l.transform="translate3d(-50%, "+n+"px, 0)"}(n)})),w=ru((e=>{t.enablePullDownRefresh&&Zg(e,u,d)&&null!==p&&("pulling"===p?(y(),p=Jg,v(),function(e){if(!a)return;if(l.transform){l.transition="-webkit-transform 0.3s",l.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),l.transition="",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{y(),p=h=m=null}))):"reached"===p&&(y(),p=Qg,v(),x()))}));function x(){a&&(l.transition="-webkit-transform 0.2s",l.transform="translate3d(-50%, "+i+"px, 0)",Sc(n,"onPullDownRefresh"))}return{onTouchstartPassive:b,onTouchmove:_,onTouchend:w,onTouchcancel:w}}const nv=ou({name:"PageBody",setup(e,t){const n=yu(),o=sn(null),r=sn(null),i=n.enablePullDownRefresh?tv(o):null,s=sn(null);return eo((()=>n.enablePullDownRefresh),(()=>{s.value=n.enablePullDownRefresh?i:null}),{immediate:!0}),()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return oi(Gg,{ref:e},null,512)}(o,n);return oi(Nr,null,[e,oi("uni-page-wrapper",ui({ref:r},s.value),[oi("uni-page-body",null,[Ko(t.slots,"default")]),null],16)])}}});const ov=ou({name:"Page",setup(e,t){let n=bu(wu());const o=n.navigationBar,r={};return yg(n),()=>oi("uni-page",{"data-page":n.route,style:r},"custom"!==o.style?[oi(Vg),rv(t),null]:[rv(t),null])}});function rv(e){return Wr(),Gr(nv,{key:0},{default:Vn((()=>[Ko(e.slots,"page")])),_:3})}const iv={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=ad;const sv=Object.assign({}),av=Object.assign;window.__uniConfig=av({globalStyle:{backgroundColor:"#121212",navigationBar:{backgroundColor:"#121212",titleText:"行情",type:"default",titleColor:"#ffffff"},isNVue:!1},tabBar:{position:"bottom",color:"#DDE3E9",selectedColor:"#fff",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",list:[{pagePath:"pages/index/index",text:"首页",iconPath:"/static/tabbar/home.png",selectedIconPath:"/static/tabbar/home-active.png"},{pagePath:"pages/futures/index",text:"合约",iconPath:"/static/tabbar/futures.png",selectedIconPath:"/static/tabbar/futures-active.png"},{pagePath:"pages/robot/index",text:"交易",iconPath:"/static/tabbar/robot.png",selectedIconPath:"/static/tabbar/robot-active.png"},{pagePath:"pages/mine/index",text:"我的",iconPath:"/static/tabbar/mine.png",selectedIconPath:"/static/tabbar/mine-active.png"}],backgroundColor:"#121212",selectedIndex:0,shown:!0},uniIdRouter:{},easycom:{autoscan:!0,custom:{"^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}},compilerVersion:"4.75"},{appId:"__UNI__0806979",appName:"卡塔",appVersion:"1.0.0",appVersionCode:"100",async:iv,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(sv).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return av(e[n]||(e[n]={}),sv[t].default),e}),{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const lv={delay:iv.delay,timeout:iv.timeout,suspensible:iv.suspensible};iv.loading&&(lv.loadingComponent={name:"SystemAsyncLoading",render:()=>oi(Wn(iv.loading))}),iv.error&&(lv.errorComponent={name:"SystemAsyncError",props:["error"],render(){return oi(Wn(iv.error),{error:this.error})}});const cv=()=>t((()=>import("./pages-login-index.BAJjaSi_.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])).then((e=>em(e.default||e))),uv=wo(av({loader:cv},lv)),dv=()=>t((()=>import("./pages-register-index.W0z0bVn4.js")),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9,12])).then((e=>em(e.default||e))),fv=wo(av({loader:dv},lv)),pv=()=>t((()=>import("./pages-index-index.CnCzbXlV.js")),__vite__mapDeps([13,7,8,9,2,14])).then((e=>em(e.default||e))),hv=wo(av({loader:pv},lv)),mv=()=>t((()=>import("./pages-share-index.uJr_5zth.js")),__vite__mapDeps([15,16,1,2,3,4,17,18,19])).then((e=>em(e.default||e))),gv=wo(av({loader:mv},lv)),vv=()=>t((()=>import("./pages-market-index.Bs2bCU3d.js")),__vite__mapDeps([20,16,1,2,3,4,17,7,8,21])).then((e=>em(e.default||e))),yv=wo(av({loader:vv},lv)),bv=()=>t((()=>import("./pages-mine-index.zbjwOq2U.js")),__vite__mapDeps([22,16,1,2,3,4,17,7,8,23])).then((e=>em(e.default||e))),_v=wo(av({loader:bv},lv)),wv=()=>t((()=>import("./pages-copy-mycopy.DB5VKriP.js")),__vite__mapDeps([24,1,2,3,4,8,7,25])).then((e=>em(e.default||e))),xv=wo(av({loader:wv},lv)),Sv=()=>t((()=>import("./pages-leader-index.ZeLpxzl6.js")),__vite__mapDeps([26,1,2,3,4,5,6,8,7,27])).then((e=>em(e.default||e))),Tv=wo(av({loader:Sv},lv)),Cv=()=>t((()=>import("./pages-mine-upload-avatar.DyiqGQSV.js")),__vite__mapDeps([28,8,2,29])).then((e=>em(e.default||e))),kv=wo(av({loader:Cv},lv)),Ev=()=>t((()=>import("./pages-switchaccount-index.Cw1W2Yv-.js")),__vite__mapDeps([30,1,2,3,4,31])).then((e=>em(e.default||e))),Ov=wo(av({loader:Ev},lv)),Lv=()=>t((()=>import("./pages-agreement-index.8vVj5LbV.js")),__vite__mapDeps([32,1,2,3,4,7,8,33])).then((e=>em(e.default||e))),$v=wo(av({loader:Lv},lv)),Av=()=>t((()=>import("./pages-legalsystem-index.D1mRVDTd.js")),__vite__mapDeps([34,1,2,3,4,7,8,35])).then((e=>em(e.default||e))),Pv=wo(av({loader:Av},lv)),Iv=()=>t((()=>import("./pages-accountset-index.B0cXz0C9.js")),__vite__mapDeps([36,1,2,3,4,37])).then((e=>em(e.default||e))),Mv=wo(av({loader:Iv},lv)),Rv=()=>t((()=>import("./pages-updatepassword-index.Dzr2YboS.js")),__vite__mapDeps([38,1,2,3,4,7,8,39])).then((e=>em(e.default||e))),Bv=wo(av({loader:Rv},lv)),Dv=()=>t((()=>import("./pages-updatesafepwd-index.BVEID7FE.js")),__vite__mapDeps([40,1,2,3,4,7,8,41])).then((e=>em(e.default||e))),jv=wo(av({loader:Dv},lv)),Nv=()=>t((()=>import("./pages-updatephone-index.C3Qxu98q.js")),__vite__mapDeps([42,1,2,3,4,7,8,43])).then((e=>em(e.default||e))),Vv=wo(av({loader:Nv},lv)),Fv=()=>t((()=>import("./pages-noticelist-index.CapRg9On.js")),__vite__mapDeps([44,1,2,3,4,7,8,45])).then((e=>em(e.default||e))),Hv=wo(av({loader:Fv},lv)),qv=()=>t((()=>import("./pages-noticedetail-index.DY1w80sY.js")),__vite__mapDeps([46,1,2,3,4,7,8,47])).then((e=>em(e.default||e))),zv=wo(av({loader:qv},lv)),Wv=()=>t((()=>import("./pages-transfer-index.BAHJD0AT.js")),__vite__mapDeps([48,1,2,3,4,7,8,49])).then((e=>em(e.default||e))),Uv=wo(av({loader:Wv},lv)),Yv=()=>t((()=>import("./pages-recharge-index.--dSLA43.js")),__vite__mapDeps([50,16,1,2,3,4,17,18,7,8,51])).then((e=>em(e.default||e))),Xv=wo(av({loader:Yv},lv)),Kv=()=>t((()=>import("./pages-reset-index.UvTBv7zh.js")),__vite__mapDeps([52,1,2,3,4,7,8,9,53])).then((e=>em(e.default||e))),Gv=wo(av({loader:Kv},lv)),Zv=()=>t((()=>import("./pages-futures-index.BmEuO7dw.js")),__vite__mapDeps([54,5,2,4,6,8,7,55])).then((e=>em(e.default||e))),Jv=wo(av({loader:Zv},lv)),Qv=()=>t((()=>import("./pages-robot-index.JQftZOFJ.js")),__vite__mapDeps([56,7,8,2,57])).then((e=>em(e.default||e))),ey=wo(av({loader:Qv},lv)),ty=()=>t((()=>import("./pages-kline-index.C_R3kEtq.js")),__vite__mapDeps([58,1,2,3,4,7,8,59])).then((e=>em(e.default||e))),ny=wo(av({loader:ty},lv)),oy=()=>t((()=>import("./pages-share-record.rOY91Nkm.js")),__vite__mapDeps([60,16,1,2,3,4,17,8,61])).then((e=>em(e.default||e))),ry=wo(av({loader:oy},lv)),iy=()=>t((()=>import("./pages-asset-index.C_FFT5bJ.js")),__vite__mapDeps([62,1,2,3,4,16,17,7,8,63])).then((e=>em(e.default||e))),sy=wo(av({loader:iy},lv)),ay=()=>t((()=>import("./pages-withdraw-index.DDNKAaw-.js")),__vite__mapDeps([64,1,2,3,4,7,8,65])).then((e=>em(e.default||e))),ly=wo(av({loader:ay},lv)),cy=()=>t((()=>import("./pages-transfer-record.DSuVYB3n.js")),__vite__mapDeps([66,1,2,3,4,7,8,67])).then((e=>em(e.default||e))),uy=wo(av({loader:cy},lv)),dy=()=>t((()=>import("./pages-withdraw-record.Cwe-gq2-.js")),__vite__mapDeps([68,1,2,3,4,7,8,69])).then((e=>em(e.default||e))),fy=wo(av({loader:dy},lv)),py=()=>t((()=>import("./pages-profit-index.BRu9zYZ6.js")),__vite__mapDeps([70,1,2,3,4,7,8,71])).then((e=>em(e.default||e))),hy=wo(av({loader:py},lv)),my=()=>t((()=>import("./pages-team-index.CZzO43pd.js")),__vite__mapDeps([72,1,2,3,4,7,8,73])).then((e=>em(e.default||e))),gy=wo(av({loader:my},lv)),vy=()=>t((()=>import("./pages-account-transfer-record.BR1CDrpi.js")),__vite__mapDeps([74,1,2,3,4,7,8,75])).then((e=>em(e.default||e))),yy=wo(av({loader:vy},lv));function by(e,t){return Wr(),Gr(ov,null,{page:Vn((()=>[oi(e,av({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/login/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(uv,t)}},loader:cv,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"登录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/register/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(fv,t)}},loader:dv,meta:{navigationBar:{titleText:"注册",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/index/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(hv,t)}},loader:pv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"首页",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/share/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(gv,t)}},loader:mv,meta:{navigationBar:{titleText:"业务分享",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/market/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(yv,t)}},loader:vv,meta:{navigationBar:{titleText:"市场",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(_v,t)}},loader:bv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,navigationBar:{titleText:"我的",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/copy/mycopy",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(xv,t)}},loader:wv,meta:{navigationBar:{titleText:"我的跟单",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/leader/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Tv,t)}},loader:Sv,meta:{navigationBar:{titleText:"带单中心",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/upload-avatar",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(kv,t)}},loader:Cv,meta:{navigationBar:{titleText:"上传头像",type:"default"},isNVue:!1}},{path:"/pages/switchaccount/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Ov,t)}},loader:Ev,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/agreement/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by($v,t)}},loader:Lv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/legalsystem/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Pv,t)}},loader:Av,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/accountset/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Mv,t)}},loader:Iv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/updatepassword/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Bv,t)}},loader:Rv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/updatesafepwd/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(jv,t)}},loader:Dv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/updatephone/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Vv,t)}},loader:Nv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/noticelist/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Hv,t)}},loader:Fv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/noticedetail/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(zv,t)}},loader:qv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/transfer/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Uv,t)}},loader:Wv,meta:{navigationBar:{style:"custom",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/recharge/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Xv,t)}},loader:Yv,meta:{navigationBar:{style:"custom",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/reset/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Gv,t)}},loader:Kv,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/futures/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(Jv,t)}},loader:Zv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/robot/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(ey,t)}},loader:Qv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/kline/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(ny,t)}},loader:ty,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/share/record",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(ry,t)}},loader:oy,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"邀请记录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/asset/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(sy,t)}},loader:iy,meta:{navigationBar:{titleText:"我的资产",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/withdraw/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(ly,t)}},loader:ay,meta:{navigationBar:{titleText:"提现",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/transfer/record",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(uy,t)}},loader:cy,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/withdraw/record",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(fy,t)}},loader:dy,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/profit/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(hy,t)}},loader:py,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/team/index",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(gy,t)}},loader:my,meta:{navigationBar:{titleText:"我的团队",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/account-transfer/record",component:{setup(){const e=Gh(),t=e&&e.$route&&e.$route.query||{};return()=>by(yy,t)}},loader:vy,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const _y={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};Qh(_y,{init:Zh,setup(e){const t=_u(),n=()=>{var n;n=e,Object.keys(Ld).forEach((e=>{Ld[e].forEach((t=>{Bo(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return c(ap,{path:e,query:t}),c(lp,ap),c({},ap)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:xe(t.query)});if(o&&M(o,s),r&&M(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};af(),i&&M(i,e)}};return br(Ha).isReady().then(n),No((()=>{window.addEventListener("resize",Ce(tm,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",nm),document.addEventListener("visibilitychange",om),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Ng.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Wr(),Gr(Og));e.setup=(e,o)=>{const r=t&&t(e,o);return g(r)?n:r},e.render=n}});(()=>{const e=()=>{};"undefined"!=typeof window&&(window._originalConsole={log:console.log,info:console.info,warn:console.warn,error:console.error,debug:console.debug}),console.log=e,console.info=e,console.warn=e,console.error=e,console.debug=e,console.trace=e,console.table=e,console.group=e,console.groupEnd=e,console.groupCollapsed=e,console.time=e,console.timeEnd=e,console.timeLog=e,console.count=e,console.countReset=e,console.clear=e,console.assert=e,console.dir=e,console.dirxml=e,"undefined"!=typeof uni&&(uni.log=e,uni.error=e,uni.warn=e,uni.info=e,uni.debug=e)})(),function(){const e=Ss(_y);return e.config.globalProperties.$log=()=>{},{app:e}}().app.use(Hh).mount("#app");export{Xi as $,nf as A,eo as B,Ne as C,ki as D,qt as E,Nr as F,Cf as G,gm as H,Gp as I,wh as J,bm as K,hg as L,mg as M,ni as N,Lm as O,qm as P,hh as Q,vh as R,bh as S,Tm as T,Cm as U,fp as V,fu as W,dp as X,Bg as Y,Ed as Z,io as _,Sm as a,Ko as a0,Wn as a1,xd as a2,Dm as a3,ym as a4,bg as a5,_m as b,ef as c,ng as d,Wm as e,Yn as f,xm as g,Gr as h,Ch as i,oi as j,si as k,Kr as l,ii as m,zm as n,Wr as o,he as p,Xo as q,rf as r,fg as s,Y as t,_s as u,Lp as v,Vn as w,Th as x,gu as y,pe as z};
