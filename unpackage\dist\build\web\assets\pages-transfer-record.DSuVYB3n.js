import{H as t,n as a,f as e,h as s,w as i,z as r,i as o,o as l,j as d,m as n,p as c,l as g,q as p,F as u,k as h,x as f,t as m}from"./index-Qsizygi4.js";import{_}from"./uni-icons.CkwqPbHP.js";import{r as T}from"./uni-app.es.KXR2Mdtb.js";import{r as y}from"./request.BPygAub8.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const b=v({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,activeTab:0,recordList:[],loading:!1,total:0,page:1,pageSize:10}),created(){const a=t();this.statusBarHeight=a.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight,this.getRecordList(!0)},methods:{handleBack(){a()},onTabChange(t){this.activeTab=t,this.getRecordList(!0)},async getRecordList(t=!1){this.loading=!0,t&&(this.page=1);const a=[2,1][this.activeTab];try{const e=await y({url:"/api/transfer/list",method:"GET",data:{page:this.page,pageSize:this.pageSize,type:a}});200===e.code&&e.data&&(this.recordList=t?e.data.records:this.recordList.concat(e.data.records),this.total=e.data.total)}finally{this.loading=!1}},loadMore(){this.recordList.length<this.total&&!this.loading&&(this.page++,this.getRecordList())},formatTime(t){if(!t)return"";const a=new Date(t);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`},getDisplayUid(t){return 0===this.activeTab?t.toUserNo||t.toUsername||"未知":t.fromUserNo||t.fromUsername||"未知"},getAmountDisplay(t){return 0===this.activeTab?`-${t.amount}USDT`:`+${t.amount}USDT`}}},[["render",function(t,a,y,v,b,k){const S=T(e("uni-icons"),_),L=o,x=f;return l(),s(L,{class:"record-container",style:r({paddingTop:b.navPaddingTop+"px"})},{default:i((()=>[d(L,{class:"custom-navbar",style:r({paddingTop:b.statusBarHeight+"px"})},{default:i((()=>[d(L,{class:"navbar-content"},{default:i((()=>[d(L,{class:"left-area",onClick:k.handleBack},{default:i((()=>[d(S,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),d(x,{class:"page-title"},{default:i((()=>[n("互转记录")])),_:1}),d(L,{class:"right-area"})])),_:1})])),_:1},8,["style"]),d(L,{class:"tab-bar"},{default:i((()=>[d(L,{class:c(["tab",{active:0===b.activeTab}]),onClick:a[0]||(a[0]=t=>k.onTabChange(0))},{default:i((()=>[n("转出记录")])),_:1},8,["class"]),d(L,{class:c(["tab",{active:1===b.activeTab}]),onClick:a[1]||(a[1]=t=>k.onTabChange(1))},{default:i((()=>[n("转入记录")])),_:1},8,["class"])])),_:1}),d(L,{class:"record-card"},{default:i((()=>[(l(!0),g(u,null,p(b.recordList,((t,a)=>(l(),s(L,{key:t.id,class:"record-item"},{default:i((()=>[d(x,{class:"record-uid"},{default:i((()=>[n(m(k.getDisplayUid(t)),1)])),_:2},1024),d(x,{class:"record-commission"},{default:i((()=>[n(m(k.getAmountDisplay(t)),1)])),_:2},1024),d(x,{class:"record-time",style:{"margin-left":"32rpx"}},{default:i((()=>[n(m(k.formatTime(t.createTime)),1)])),_:2},1024)])),_:2},1024)))),128)),b.loading?(l(),s(L,{key:0,class:"loading-text"},{default:i((()=>[n("加载中...")])),_:1})):h("",!0),b.loading||0!==b.recordList.length?h("",!0):(l(),s(L,{key:1,class:"empty-text"},{default:i((()=>[n("暂无数据")])),_:1})),!b.loading&&b.recordList.length<b.total?(l(),s(L,{key:2,class:"load-more",onClick:k.loadMore},{default:i((()=>[n("加载更多")])),_:1},8,["onClick"])):h("",!0)])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-f3dd71ec"]]);export{b as default};
