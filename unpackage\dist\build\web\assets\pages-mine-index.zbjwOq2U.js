import{b as a,A as s,g as t,e,s as o,L as n,M as l,h as i,w as c,i as r,o as d,j as u,m as p,t as m,k as f,l as A,q as h,F as g,u as C,v as k,x as w,y,I as T}from"./index-Qsizygi4.js";import{C as I}from"./custom-navbar.D7fsSS_d.js";import{r as B}from"./request.BPygAub8.js";import{c as Q}from"./index.B6QF5Ba_.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.CkwqPbHP.js";import"./uni-app.es.KXR2Mdtb.js";const b=v({components:{CustomNavbar:I},data:()=>({userEmail:"",userNo:"",availableBalance:"0.00",copyTradeBalance:"0.00",commissionBalance:"0.00",usageFrozenBalance:"0.00",profitBalance:"0.00",inviteCode:"",userType:"",carbonPoints:"",exchangeVouchers:"",dynamicQuota:"",waitExchangeGb:"",catBalance:"0.00",isActivated:!1,commissionRate:0,assetsMenu:[{icon:"/static/icons/svg/carbon.svg",text:"我的资产",type:"carbon"},{icon:"/static/icons/svg/gift.svg",text:"我的团队",type:"gift"},{icon:"/static/icons/svg/exchange.svg",text:"收益明细",type:"exchange"},{icon:"/static/icons/svg/battery.svg",text:"人人有礼",type:"battery"}],accountList:[{name:"资金账户",type:"fund"},{name:"佣金账户",type:"commission"},{name:"跟单账户",type:"copy"},{name:"利润账户",type:"profit"}],showTransferPopup:!1,showPasswordPopup:!1,transferFrom:null,transferTo:null,transferAmount:"",payPassword:"",showFromDropdown:!1,showToDropdown:!1,todayCommission:null,upCount:null,downCount:null,statsLoading:!0,avatarUrl:"/static/tools/default.png",defaultAvatar:"/static/tools/default.png",isLeader:!1}),computed:{availableToAccounts(){if(null===this.transferFrom)return this.accountList;switch(this.accountList[this.transferFrom].type){case"fund":return this.accountList.filter((a=>"copy"===a.type));case"copy":return this.accountList.filter((a=>"fund"===a.type));case"commission":case"profit":return this.accountList.filter((a=>"fund"===a.type||"copy"===a.type));default:return[]}}},onShow(){this.loadUserInfo(),this.loadTodayStats()},methods:{formatAccount:a=>a&&""!==a?a.length>7?a.substr(0,3)+"****"+a.substr(-4):a:"",maskEmail(a){if(!a)return"";const s=a.indexOf("@");if(s<=2)return a;return a.slice(0,2)+"****"+a.slice(s-1,s)+a.slice(s)},getAvatarUrl:a=>a?a.startsWith("http")?a:Q.apiBaseUrl+a:"/static/tools/default.png",onAvatarError(){this.avatarUrl=this.defaultAvatar},async loadUserInfo(){try{const t=await B({url:"/api/user/info",method:"GET"});if(200===t.code&&t.data){if(this.userEmail=t.data.email||"",this.userNo=t.data.userNo||"",this.availableBalance=t.data.availableBalance||"0.00",this.copyTradeBalance=t.data.copyTradeBalance||"0.00",this.commissionBalance=t.data.commissionBalance||"0.00",this.profitBalance=t.data.profitBalance||"0.00",this.inviteCode=t.data.shareCode,this.catBalance=t.data.catBalance||"0.00",this.avatarUrl=this.getAvatarUrl(t.data.avatar),this.isActivated=t.data.isActivated,this.commissionRate=t.data.commissionRate,a("userInfo",t.data),!t.data.status)return void s({url:"/pages/login/index"});this.isLeader=1===t.data.isLeader}}catch(e){console.error("获取用户信息失败:",e);const a=t("userInfo");a&&(this.userEmail=a.email||"",this.userNo=a.userNo||"",this.availableBalance=a.availableBalance||"0.00",this.copyTradeBalance=a.copyTradeBalance||"0.00",this.commissionBalance=a.commissionBalance||"0.00",this.profitBalance=a.profitBalance||"0.00",this.catBalance=a.catBalance||"0.00",this.avatarUrl=this.getAvatarUrl(a.avatar),this.isLeader=1===a.isLeader)}},async loadTodayStats(){this.statsLoading=!0;try{const a=await B({url:"/api/user/today-stats",method:"GET"});200===a.code&&a.data?(this.todayCommission=a.data.todayCommission,this.upCount=a.data.upCount,this.downCount=a.data.downCount):(this.todayCommission=0,this.upCount=0,this.downCount=0)}catch(a){this.todayCommission=0,this.upCount=0,this.downCount=0}finally{this.statsLoading=!1}},handleRecharge(){console.log("充值"),e({url:"/pages/recharge/index"})},handleTransfer(){this.showTransferPopup=!0},toggleFromDropdown(){this.showFromDropdown=!this.showFromDropdown,this.showToDropdown=!1},toggleToDropdown(){this.showToDropdown=!this.showToDropdown,this.showFromDropdown=!1},selectFrom(a){this.transferFrom=a,this.transferTo=null,this.showFromDropdown=!1},selectTo(a){this.transferTo=a,this.showToDropdown=!1},closeTransferPopup(){this.showTransferPopup=!1,this.transferAmount="",this.payPassword="",this.showFromDropdown=!1,this.showToDropdown=!1,this.transferFrom=null,this.transferTo=null},confirmTransfer(){if(null===this.transferFrom||null===this.transferTo)return void o({title:"请选择划转账户",icon:"none"});const a=Number(this.transferAmount);if(!this.transferAmount||""===this.transferAmount.trim())return void o({title:"请输入划转金额",icon:"none"});if(isNaN(a)||a<=0)return void o({title:"请输入有效金额（大于0）",icon:"none"});a>Number(this.getAccountBalance(this.transferFrom))?o({title:"余额不足",icon:"none"}):this.showPasswordPopup=!0},validateTransferRule:(a,s)=>"fund"===a&&"copy"===s||"copy"===a&&"fund"===s||("commission"===a&&("fund"===s||"copy"===s)||"profit"===a&&("fund"===s||"copy"===s)),closePasswordPopup(){this.showPasswordPopup=!1,this.payPassword=""},submitTransfer(){if(!this.payPassword)return void o({title:"请输入支付密码",icon:"none",duration:2e3,mask:!0});n({title:"划转中...",mask:!0});const a={fromAccountType:this.accountList[this.transferFrom].type,toAccountType:this.accountList[this.transferTo].type,amount:Number(this.transferAmount),payPassword:this.payPassword};B({url:"/api/user/transfer",method:"POST",data:a}).then((a=>{l(),200===a.code?(o({title:"划转成功",icon:"success",duration:2e3,mask:!0}),setTimeout((()=>{this.showPasswordPopup=!1,this.showTransferPopup=!1,this.transferAmount="",this.payPassword="",this.loadUserInfo()}),1500)):o({title:a.message||"划转失败",icon:"none",duration:2e3,mask:!0})})).catch((a=>{l(),console.error("划转失败:",a);let s="网络错误，请重试";a.response&&a.response.data&&a.response.data.message?s=a.response.data.message:a.message&&(s=a.message),o({title:s,icon:"none",duration:2e3,mask:!0})}))},handleTeam(){e({url:"/pages/team/index"})},handleInvite(){e({url:"/pages/share/index"})},goToAsset(){e({url:"/pages/asset/index"})},goToProfit(){e({url:"/pages/profit/index"})},handleWithdraw(){console.log("提现按钮被点击"),e({url:"/pages/withdraw/index"})},getAccountBalance(a){var s;const t=null==(s=this.accountList[a])?void 0:s.type;return"fund"===t?this.availableBalance:"commission"===t?this.commissionBalance:"copy"===t?this.copyTradeBalance:"profit"===t?this.profitBalance:"0.00"},goToUploadAvatar(){e({url:"/pages/mine/upload-avatar"})},goToLeaderCenter(){e({url:"/pages/leader/index"})}}},[["render",function(a,s,t,e,o,n){const l=k,I=w,B=r,Q=y,v=T;return d(),i(B,{class:"container"},{default:c((()=>[u(B,{class:"user-info glass-effect animate-item"},{default:c((()=>[u(l,{class:"avatar",src:o.avatarUrl,onClick:n.goToUploadAvatar,onError:n.onAvatarError},null,8,["src","onClick","onError"]),u(B,{class:"user-details"},{default:c((()=>[u(I,{class:"account"},{default:c((()=>[p("账号："+m(n.maskEmail(o.userEmail)),1)])),_:1}),u(I,{class:"uid"},{default:c((()=>[p("UID："+m(o.userNo),1)])),_:1}),u(I,{class:"invite-code"},{default:c((()=>[p("CAT："+m(Number(o.catBalance).toFixed(4)),1)])),_:1}),o.commissionRate>0&&o.isActivated?(d(),i(I,{key:0,class:"invite-code"},{default:c((()=>[p("佣金比例："+m(o.commissionRate)+"%",1)])),_:1})):f("",!0)])),_:1})])),_:1}),u(B,{class:"stats-card glass-effect animate-item"},{default:c((()=>[u(B,{class:"stats-btn-row"},{default:c((()=>[u(Q,{class:"recharge-btn",onClick:n.handleRecharge},{default:c((()=>[p("充值")])),_:1},8,["onClick"]),u(Q,{class:"withdraw-btn",style:{"z-index":"999"},onClick:n.handleWithdraw},{default:c((()=>[p("提现")])),_:1},8,["onClick"]),u(Q,{class:"transfer-btn",onClick:n.handleTransfer},{default:c((()=>[p("划转")])),_:1},8,["onClick"])])),_:1}),u(B,{class:"account-grid"},{default:c((()=>[u(B,{class:"account-row"},{default:c((()=>[u(B,{class:"account-card",onClick:n.goToAsset},{default:c((()=>[u(I,{class:"account-name"},{default:c((()=>[p("资金账户(USDT)")])),_:1}),u(I,{class:"account-balance"},{default:c((()=>[p(m(Number(o.availableBalance).toFixed(4)),1)])),_:1})])),_:1},8,["onClick"]),u(B,{class:"account-card"},{default:c((()=>[u(I,{class:"account-name"},{default:c((()=>[p("跟单账户(USDT)")])),_:1}),u(I,{class:"account-balance"},{default:c((()=>[p(m(Number(o.copyTradeBalance).toFixed(4)),1)])),_:1})])),_:1})])),_:1}),u(B,{class:"account-row"},{default:c((()=>[u(B,{class:"account-card"},{default:c((()=>[u(I,{class:"account-name"},{default:c((()=>[p("佣金账户(USDT)")])),_:1}),u(I,{class:"account-balance"},{default:c((()=>[p(m(Number(o.commissionBalance).toFixed(4)),1)])),_:1})])),_:1}),u(B,{class:"account-card"},{default:c((()=>[u(I,{class:"account-name"},{default:c((()=>[p("利润账户(USDT)")])),_:1}),u(I,{class:"account-balance"},{default:c((()=>[p(m(Number(o.profitBalance).toFixed(4)),1)])),_:1})])),_:1})])),_:1})])),_:1}),u(B,{class:"stats-summary"},{default:c((()=>[u(B,{class:"summary-item main"},{default:c((()=>[u(I,{class:"summary-label"},{default:c((()=>[p("今日收益(USDT)")])),_:1}),u(I,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":+Number(o.todayCommission).toFixed(4)),1)])),_:1})])),_:1}),u(B,{class:"summary-item"},{default:c((()=>[u(I,{class:"summary-label"},{default:c((()=>[p("买涨(次)")])),_:1}),u(I,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":o.upCount),1)])),_:1})])),_:1}),u(B,{class:"summary-item"},{default:c((()=>[u(I,{class:"summary-label"},{default:c((()=>[p("买跌(次)")])),_:1}),u(I,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":o.downCount),1)])),_:1})])),_:1})])),_:1})])),_:1}),u(B,{class:"asset-extra-card"},{default:c((()=>[u(B,{class:"extra-item team",onClick:n.handleTeam},{default:c((()=>[u(B,{class:"extra-icon team-icon"},{default:c((()=>[u(l,{src:"/assets/team-DflAYIix.png",mode:"aspectFit"})])),_:1}),u(I,{class:"extra-label"},{default:c((()=>[p("我的团队")])),_:1})])),_:1},8,["onClick"]),u(B,{class:"extra-item profit",onClick:n.goToProfit},{default:c((()=>[u(B,{class:"extra-icon profit-icon"},{default:c((()=>[u(l,{src:"data:image/png;base64,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",mode:"aspectFit"})])),_:1}),u(B,{class:"extra-info"},{default:c((()=>[u(I,{class:"extra-label"},{default:c((()=>[p("佣金/收益明细")])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1}),o.isLeader?(d(),i(B,{key:0,class:"leader-center-card",onClick:n.goToLeaderCenter},{default:c((()=>[u(B,{class:"leader-content"},{default:c((()=>[u(B,{class:"leader-icon-wrapper"},{default:c((()=>[u(l,{src:"/assets/daidan-CpNW83Bs.png",mode:"aspectFit",class:"leader-icon"})])),_:1}),u(B,{class:"leader-info"},{default:c((()=>[u(I,{class:"leader-title"},{default:c((()=>[p("带单员中心")])),_:1}),u(I,{class:"leader-desc"},{default:c((()=>[p("管理您的带单业务")])),_:1})])),_:1}),u(l,{src:"/assets/gold-D0bJBoCs.png",mode:"aspectFit",class:"leader-gold-img"})])),_:1})])),_:1},8,["onClick"])):f("",!0),o.isLeader?(d(),i(B,{key:1,class:"section-divider"})):f("",!0),u(B,{class:"invite-banner",onClick:n.handleInvite},{default:c((()=>[u(B,{class:"invite-content"},{default:c((()=>[u(B,{class:"invite-icon-wrapper"},{default:c((()=>[u(l,{src:"/assets/yaoqing-jVDFoiCd.png",mode:"aspectFit",class:"invite-icon"})])),_:1}),u(B,{class:"invite-text"},{default:c((()=>[u(I,{class:"invite-title"},{default:c((()=>[p("人人有礼")])),_:1}),u(I,{class:"invite-desc"},{default:c((()=>[p("邀请好友得奖励")])),_:1})])),_:1})])),_:1}),u(l,{class:"invite-img",src:"/assets/lipin-DbXBzme3.png",mode:"aspectFit"})])),_:1},8,["onClick"]),o.showTransferPopup?(d(),i(B,{key:2,class:"transfer-popup"},{default:c((()=>[u(B,{class:"popup-content",onClick:s[1]||(s[1]=C((()=>{}),["stop"]))},{default:c((()=>[u(B,{class:"popup-title"},{default:c((()=>[p("账户划转")])),_:1}),u(B,{class:"custom-select",onClick:n.toggleFromDropdown},{default:c((()=>[u(B,{class:"picker-label"},{default:c((()=>[p("从账户")])),_:1}),u(B,{class:"picker-value"},{default:c((()=>{var a;return[p(m((null==(a=o.accountList[o.transferFrom])?void 0:a.name)||"请选择"),1)]})),_:1}),u(B,{class:"arrow"},{default:c((()=>[p("▼")])),_:1}),o.showFromDropdown?(d(),i(B,{key:0,class:"dropdown-list"},{default:c((()=>[(d(!0),A(g,null,h(o.accountList,((a,s)=>(d(),i(B,{class:"dropdown-item",key:s,onClick:C((a=>n.selectFrom(s)),["stop"])},{default:c((()=>[p(m(a.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})):f("",!0)])),_:1},8,["onClick"]),u(B,{class:"custom-select",onClick:n.toggleToDropdown},{default:c((()=>[u(B,{class:"picker-label"},{default:c((()=>[p("到账户")])),_:1}),u(B,{class:"picker-value"},{default:c((()=>{var a;return[p(m((null==(a=o.accountList[o.transferTo])?void 0:a.name)||"请选择"),1)]})),_:1}),u(B,{class:"arrow"},{default:c((()=>[p("▼")])),_:1}),o.showToDropdown?(d(),i(B,{key:0,class:"dropdown-list"},{default:c((()=>[(d(!0),A(g,null,h(n.availableToAccounts,((a,s)=>(d(),i(B,{class:"dropdown-item",key:s,onClick:C((s=>n.selectTo(o.accountList.findIndex((s=>s.type===a.type)))),["stop"])},{default:c((()=>[p(m(a.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})):f("",!0)])),_:1},8,["onClick"]),u(B,{class:"balance-row"},{default:c((()=>[p("余额："+m(n.getAccountBalance(o.transferFrom))+" USDT",1)])),_:1}),u(B,{class:"input-row"},{default:c((()=>[u(v,{class:"amount-input",modelValue:o.transferAmount,"onUpdate:modelValue":s[0]||(s[0]=a=>o.transferAmount=a),type:"number",placeholder:"请输入划转金额"},null,8,["modelValue"])])),_:1}),u(Q,{class:"confirm-btn",onClick:n.confirmTransfer},{default:c((()=>[p("确定")])),_:1},8,["onClick"]),u(B,{class:"close-btn",onClick:n.closeTransferPopup},{default:c((()=>[p("×")])),_:1},8,["onClick"])])),_:1})])),_:1})):f("",!0),o.showPasswordPopup?(d(),i(B,{key:3,class:"password-popup",onClick:n.closePasswordPopup},{default:c((()=>[u(B,{class:"popup-content",onClick:s[3]||(s[3]=C((()=>{}),["stop"]))},{default:c((()=>[u(B,{class:"popup-title"},{default:c((()=>[p("请输入支付密码")])),_:1}),u(v,{class:"password-input",modelValue:o.payPassword,"onUpdate:modelValue":s[2]||(s[2]=a=>o.payPassword=a),type:"password",placeholder:"支付密码"},null,8,["modelValue"]),u(Q,{class:"confirm-btn",onClick:n.submitTransfer},{default:c((()=>[p("确认划转")])),_:1},8,["onClick"]),u(B,{class:"close-btn",onClick:n.closePasswordPopup},{default:c((()=>[p("×")])),_:1},8,["onClick"])])),_:1})])),_:1},8,["onClick"])):f("",!0)])),_:1})}],["__scopeId","data-v-2f939da1"]]);export{b as default};
