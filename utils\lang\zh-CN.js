export default {
  // ====== 公共 ======
  confirm: '确定', // 公用
  cancel: '取消', // 公用
  submitting: '提交中...', // 公用
  language: '语言', // 公用
  download: '点击下载', // 公用
  fail: '失败', // 公用
  operate: '操作', // 公用
  appName: 'Catcoin',

  // ====== 登录页面 login ======
  welcome: '欢迎登录：Catcoin',
  phoneLogin: '手机号登录',
  emailLogin: '邮箱登录',
  password: '密码',
  forgotPassword: '忘记密码',
  register: '立即注册',
  login: '登录',
  addAccount: '添加账号',
  relogin: '重新登录',
  zhCN: '简体中文',
  zhTW: '繁体中文',
  accountLocked: '账号已被锁定',
  inputPhone: '请输入手机号',
  inputPhoneValid: '请输入正确的手机号',
  inputEmail: '请输入邮箱',
  inputEmailValid: '请输入正确的邮箱',
  inputPassword: '请输入密码',
  sendSuccess: '发送成功',
  sendFail: '发送失败',
  switchSuccess: '切换成功',
  addSuccess: '添加成功',
  loginSuccess: '登录成功',
  loginLoading: '登录中...',
  inputConfirmPwd: '请再次输入新口令',
  inputEmailCode: '请输入验证码',

  // ====== 重置密码页面 reset ======
  resetTitle: '重置密码',
  resetSubtitle: '请通过手机或邮箱验证身份',
  resetSubtitleEmail: '请通过邮箱身份验证',
  phoneTab: '手机找回',
  emailTab: '邮箱找回',
  getCode: '获取验证码',
  inputCode: '请输入验证码',
  inputNewPwd: '请输入新密码',
  pwdMinLen: '密码不能少于6位',
  pwdNotMatch: '两次密码输入不一致',
  codeSent: '验证码已发送',
  resetSuccess: '密码重置成功',
  resetFail: '重置失败',
  confirmModify: '确认修改',
  langSetting: '语言设置',
  sending:'发送中',

  // ====== 注册页面 register ======
  registerWelcome: '欢迎登录：Catcoin',
  registerTitle: 'Welcome!',
  registerPhoneTab: '手机号码注册',
  registerEmailTab: '邮箱注册',
  inputName: '请输入昵称',
  inputConfirmPassword: '请再次输入密码',
  inputSecurityPassword: '请输入安全密码',
  inputReferrerCode: '请输入邀请码',
  registerNow: '立即注册',
  registering: '注册中...',
  captchaTitle: '请输入下图验证码',
  captchaPlaceholder: '请输入图片验证码',
  captchaRefresh: '换一张',
  captchaConfirm: '确定',
  captchaCancel: '取消',
  sendCode: '发送验证码',
  getCaptchaFail: '获取验证码失败',
  getCaptchaImgFail: '获取验证码图片失败',
  phoneRegistered: '该手机号已被注册',
  registerSuccess: '注册成功',
  registerFail: '注册失败,请重试',
  inputNameMax: '昵称最多输入8个字符',
  securityPwdMinLen: '安全密码不能少于6位',
  operateFail: '操作失败，请重试',

   // ====== 首页相关 ======
   marketTrend: '市场趋势', // 首页市场趋势标题
   notice: '通知', // 公告栏通知
   fetchLatestDataFail: '获取最新数据失败', // 获取用户信息失败toast
   loadDataFail: '加载数据失败', // 加载数据失败toast
  // ====== 合约页面 ======
   futuresTitle: '合约', // 合约页面标题
   // ====== 机器人页面 ======
   robotTitle: '机器人', // 机器人页面标题
} 