import{H as a,n as e,e as t,s,f as l,h as n,w as o,i as r,o as c,j as i,m as u,z as d,t as m,k as f,x as h,Y as p,I as g,y as _}from"./index-Qsizygi4.js";import{_ as b}from"./uni-icons.CkwqPbHP.js";import{r as T}from"./uni-app.es.KXR2Mdtb.js";import{r as y}from"./request.BPygAub8.js";import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const k=w({data:()=>({statusBarHeight:0,accountTypes:["UID","邮箱"],accountTypeIndex:0,targetAccount:"",amount:"",availableAmount:"0",payPwd:"",remark:"",transferParams:{minTransfer:0,maxTransfer:0,transferFee:0,enableTransfer:!0},fee:0,showConfirmDialog:!1}),watch:{amount(a){this.calcFee()}},created(){const e=a();this.statusBarHeight=e.statusBarHeight,this.getTransferParams().then((()=>{this.getUserInfo()}))},methods:{handleBack(){e()},goToRecord(){t({url:"/pages/transfer/record"})},onAccountTypeChange(a){this.accountTypeIndex=a.detail.value,this.targetAccount=""},handleSubmit(){this.targetAccount&&this.amount&&this.payPwd?this.transferParams.enableTransfer?Number(this.amount)<Number(this.transferParams.minTransfer)?s({title:`最小转账金额为${this.transferParams.minTransfer}`,icon:"none"}):Number(this.amount)>Number(this.transferParams.maxTransfer)?s({title:`最大转账金额为${this.transferParams.maxTransfer}`,icon:"none"}):Number(this.amount)<=Number(this.fee)?s({title:"金额需大于手续费",icon:"none"}):this.showConfirmDialog=!0:s({title:"当前不允许转账",icon:"none"}):s({title:"请填写完整信息",icon:"none"})},confirmTransfer(){this.showConfirmDialog=!1,this.submitTransfer()},async getTransferParams(){try{const a=await y({url:"/api/sys/params/transfer-withdraw",method:"GET"});200===a.code&&a.data&&(this.transferParams=a.data)}catch(a){s({title:"获取转账参数失败",icon:"none"})}},async getUserInfo(){try{const a=await y({url:"/api/user/info",method:"GET"});200===a.code&&a.data&&(this.availableAmount=a.data.availableBalance||"0")}catch(a){this.availableAmount="0"}},calcFee(){const a=Number(this.amount),e=Number(this.transferParams.transferFee||0);isNaN(a)||isNaN(e)?this.fee=0:this.fee=e},async submitTransfer(){let a={amount:this.amount,remark:this.remark,payPwd:this.payPwd};this.targetAccount.includes("@")?a.toEmail=this.targetAccount:a.toUid=this.targetAccount;try{const e=await y({url:"/api/transfer/create",method:"POST",data:a});200===e.code?(s({title:"转账成功",icon:"success"}),this.targetAccount="",this.amount="",this.remark="",this.payPwd="",this.getUserInfo()):s({title:e.msg||"转账失败",icon:"none"})}catch(e){s({title:e.message,icon:"none"})}}}},[["render",function(a,e,t,s,y,w){const k=T(l("uni-icons"),b),x=r,C=h,P=p,U=g,A=_;return c(),n(x,{class:"transfer-container"},{default:o((()=>[i(x,{class:"custom-navbar",style:d({paddingTop:y.statusBarHeight+"px"})},{default:o((()=>[i(x,{class:"navbar-content"},{default:o((()=>[i(x,{class:"left-area",onClick:w.handleBack},{default:o((()=>[i(k,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),i(C,{class:"page-title"},{default:o((()=>[u("互转")])),_:1}),i(x,{class:"right-area"},{default:o((()=>[i(C,{class:"record-link",onClick:w.goToRecord},{default:o((()=>[u("互转记录")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),i(x,{class:"scroll-content"},{default:o((()=>[i(x,{class:"form-section"},{default:o((()=>[i(x,{class:"form-item"},{default:o((()=>[i(C,{class:"label"},{default:o((()=>[u("账户类型")])),_:1}),i(P,{range:y.accountTypes,value:y.accountTypeIndex,onChange:w.onAccountTypeChange},{default:o((()=>[i(x,{class:"input select"},{default:o((()=>[u(m(y.accountTypes[y.accountTypeIndex]),1)])),_:1})])),_:1},8,["range","value","onChange"])])),_:1}),i(x,{class:"form-item"},{default:o((()=>[i(C,{class:"label"},{default:o((()=>[u("互转账号")])),_:1}),i(U,{class:"input",placeholder:1===y.accountTypeIndex?"请输入邮箱号":"请输入UID",modelValue:y.targetAccount,"onUpdate:modelValue":e[0]||(e[0]=a=>y.targetAccount=a)},null,8,["placeholder","modelValue"])])),_:1}),i(x,{class:"form-item amount-row"},{default:o((()=>[i(x,{class:"amount-label-row"},{default:o((()=>[i(C,{class:"label"},{default:o((()=>[u("金额")])),_:1}),i(C,{class:"available"},{default:o((()=>[u("可用："+m(y.availableAmount)+"USDT",1)])),_:1})])),_:1}),i(U,{class:"input",placeholder:"请输入金额",modelValue:y.amount,"onUpdate:modelValue":e[1]||(e[1]=a=>y.amount=a),type:"number"},null,8,["modelValue"])])),_:1}),i(x,{class:"form-item"},{default:o((()=>[i(C,{class:"label"},{default:o((()=>[u("支付密码")])),_:1}),i(U,{class:"input",placeholder:"请输入支付密码",modelValue:y.payPwd,"onUpdate:modelValue":e[2]||(e[2]=a=>y.payPwd=a),password:""},null,8,["modelValue"])])),_:1}),i(x,{class:"form-item"},{default:o((()=>[i(C,{class:"label"},{default:o((()=>[u("备注")])),_:1}),i(U,{class:"input",placeholder:"最多15字",modelValue:y.remark,"onUpdate:modelValue":e[3]||(e[3]=a=>y.remark=a),maxlength:"15"},null,8,["modelValue"])])),_:1}),i(x,{class:"result-action-row"},{default:o((()=>[i(x,{class:"result-info"},{default:o((()=>[i(C,{class:"result"},{default:o((()=>[u("到账数量："+m(y.amount&&null!==y.fee?(Number(y.amount)-Number(y.fee)).toFixed(2):0)+"USDT",1)])),_:1}),i(C,{class:"result"},{default:o((()=>[u("网络手续费："+m(y.fee)+"USDT",1)])),_:1})])),_:1}),i(A,{class:"submit-btn",onClick:w.handleSubmit},{default:o((()=>[u("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),y.showConfirmDialog?(c(),n(x,{key:0,class:"custom-dialog-mask"},{default:o((()=>[i(x,{class:"custom-dialog"},{default:o((()=>[i(x,{class:"dialog-title"},{default:o((()=>[u("请确认转账信息")])),_:1}),i(x,{class:"dialog-row"},{default:o((()=>[u("转账类型："+m(y.accountTypes[y.accountTypeIndex]),1)])),_:1}),i(x,{class:"dialog-row"},{default:o((()=>[u(m(1===y.accountTypeIndex?"邮箱":"UID")+"："+m(y.targetAccount),1)])),_:1}),i(x,{class:"dialog-row"},{default:o((()=>[u("转账金额："+m(y.amount)+" USDT",1)])),_:1}),i(x,{class:"dialog-row"},{default:o((()=>[u("到账数量："+m(y.amount&&null!==y.fee?(Number(y.amount)-Number(y.fee)).toFixed(2):0)+" USDT",1)])),_:1}),i(x,{class:"dialog-row"},{default:o((()=>[u("手续费："+m(y.fee)+" USDT",1)])),_:1}),y.remark?(c(),n(x,{key:0,class:"dialog-row"},{default:o((()=>[u("备注："+m(y.remark),1)])),_:1})):f("",!0),i(x,{class:"dialog-actions"},{default:o((()=>[i(A,{class:"dialog-btn cancel",onClick:e[4]||(e[4]=a=>y.showConfirmDialog=!1)},{default:o((()=>[u("取消")])),_:1}),i(A,{class:"dialog-btn confirm",onClick:w.confirmTransfer},{default:o((()=>[u("确认转账")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1})}],["__scopeId","data-v-db9be3b2"]]);export{k as default};
