import{H as a,n as t,f as s,h as i,w as o,z as e,i as l,o as r,j as c,m as n,t as m,p as d,l as u,q as f,F as g,k as h,x as p}from"./index-Qsizygi4.js";import{_ as y}from"./uni-icons.CkwqPbHP.js";import{r as _}from"./uni-app.es.KXR2Mdtb.js";import{r as T}from"./request.BPygAub8.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const L=k({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,activeTab:0,commissionList:[],profitList:[],loading:!1,commissionTotal:0,profitTotal:0,commissionPage:1,profitPage:1,pageSize:10,summaryData:{totalCommission:0,totalProfit:0}}),created(){const t=a();this.statusBarHeight=t.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight,this.getSummaryData(),this.getProfitList(!0)},methods:{handleBack(){t()},async getSummaryData(){try{const a=await T({url:"/api/commission/summary",method:"GET"});200===a.code&&a.data&&(this.summaryData=a.data)}catch(a){console.error("获取汇总数据失败:",a)}},onTabChange(a){this.activeTab=a,0===a?this.getProfitList(!0):this.getCommissionList(!0)},async getCommissionList(a=!1){this.loading=!0,a&&(this.commissionPage=1);try{const t=await T({url:"/api/commission/list",method:"GET",data:{page:this.commissionPage,size:this.pageSize,types:1}});200===t.code&&t.data&&(this.commissionList=a?t.data.records:this.commissionList.concat(t.data.records),this.commissionTotal=t.data.total)}finally{this.loading=!1}},async getProfitList(a=!1){this.loading=!0,a&&(this.profitPage=1);try{const t=await T({url:"/api/commission/list",method:"GET",data:{page:this.profitPage,size:this.pageSize,types:2}});200===t.code&&t.data&&(this.profitList=a?t.data.records:this.profitList.concat(t.data.records),this.profitTotal=t.data.total)}finally{this.loading=!1}},loadMore(){this.loading||(0===this.activeTab?this.profitList.length<this.profitTotal&&(this.profitPage++,this.getProfitList()):this.commissionList.length<this.commissionTotal&&(this.commissionPage++,this.getCommissionList()))},formatTime(a){if(!a)return"";const t=new Date(a);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`}}},[["render",function(a,t,T,k,L,v){const b=_(s("uni-icons"),y),P=l,C=p;return r(),i(P,{class:"profit-container",style:e({paddingTop:L.navPaddingTop+"px"})},{default:o((()=>[c(P,{class:"custom-navbar",style:e({paddingTop:L.statusBarHeight+"px"})},{default:o((()=>[c(P,{class:"navbar-content"},{default:o((()=>[c(P,{class:"left-area",onClick:v.handleBack},{default:o((()=>[c(b,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),c(C,{class:"page-title"},{default:o((()=>[n("佣金/收益")])),_:1}),c(P,{class:"right-area"})])),_:1})])),_:1},8,["style"]),c(P,{class:"profit-summary"},{default:o((()=>[c(P,{class:"summary-row"},{default:o((()=>[c(P,{class:"summary-col"},{default:o((()=>[c(C,{class:"summary-label"},{default:o((()=>[n("累积收益(USDT)")])),_:1}),c(C,{class:"summary-value"},{default:o((()=>[n(m(L.summaryData.totalProfit||0),1)])),_:1})])),_:1}),c(P,{class:"summary-col"},{default:o((()=>[c(C,{class:"summary-label"},{default:o((()=>[n("累计佣金(USDT)")])),_:1}),c(C,{class:"summary-value"},{default:o((()=>[n(m(L.summaryData.totalCommission||0),1)])),_:1})])),_:1})])),_:1})])),_:1}),c(P,{class:"tab-bar"},{default:o((()=>[c(P,{class:d(["tab",{active:0===L.activeTab}]),onClick:t[0]||(t[0]=a=>v.onTabChange(0))},{default:o((()=>[n("收益记录")])),_:1},8,["class"]),c(P,{class:d(["tab",{active:1===L.activeTab}]),onClick:t[1]||(t[1]=a=>v.onTabChange(1))},{default:o((()=>[n("佣金记录")])),_:1},8,["class"])])),_:1}),0===L.activeTab?(r(),i(P,{key:0,class:"record-card"},{default:o((()=>[(r(!0),u(g,null,f(L.profitList,((a,t)=>(r(),i(P,{key:a.id||a.userNo,class:"record-item"},{default:o((()=>[c(C,{class:"record-index"},{default:o((()=>[n(m(t+1),1)])),_:2},1024),c(C,{class:"record-amount"},{default:o((()=>[n(m(a.commissionAmount||0),1)])),_:2},1024),c(C,{class:"record-time"},{default:o((()=>[n(m(v.formatTime(a.createTime)),1)])),_:2},1024)])),_:2},1024)))),128)),L.loading?(r(),i(P,{key:0,class:"loading-text"},{default:o((()=>[n("加载中...")])),_:1})):h("",!0),L.loading||0!==L.profitList.length?h("",!0):(r(),i(P,{key:1,class:"empty-text"},{default:o((()=>[n("暂无数据")])),_:1})),!L.loading&&L.profitList.length<L.profitTotal?(r(),i(P,{key:2,class:"load-more",onClick:v.loadMore},{default:o((()=>[n("加载更多")])),_:1},8,["onClick"])):h("",!0)])),_:1})):h("",!0),1===L.activeTab?(r(),i(P,{key:1,class:"record-card"},{default:o((()=>[(r(!0),u(g,null,f(L.commissionList,((a,t)=>(r(),i(P,{key:a.id||a.userNo,class:"record-item"},{default:o((()=>[c(C,{class:"record-index"},{default:o((()=>[n(m(t+1),1)])),_:2},1024),c(C,{class:"record-amount"},{default:o((()=>[n(m(a.commissionAmount||0),1)])),_:2},1024),c(C,{class:"record-time"},{default:o((()=>[n(m(v.formatTime(a.createTime)),1)])),_:2},1024)])),_:2},1024)))),128)),L.loading?(r(),i(P,{key:0,class:"loading-text"},{default:o((()=>[n("加载中...")])),_:1})):h("",!0),L.loading||0!==L.commissionList.length?h("",!0):(r(),i(P,{key:1,class:"empty-text"},{default:o((()=>[n("暂无数据")])),_:1})),!L.loading&&L.commissionList.length<L.commissionTotal?(r(),i(P,{key:2,class:"load-more",onClick:v.loadMore},{default:o((()=>[n("加载更多")])),_:1},8,["onClick"])):h("",!0)])),_:1})):h("",!0)])),_:1},8,["style"])}],["__scopeId","data-v-8a79a267"]]);export{L as default};
