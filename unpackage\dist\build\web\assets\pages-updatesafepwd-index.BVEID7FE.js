import{H as e,s as t,L as s,n as a,M as l,f as o,h as i,w as n,i as r,o as c,j as d,m as u,z as f,t as m,x as h,I as p,y}from"./index-Qsizygi4.js";import{_ as g}from"./uni-icons.CkwqPbHP.js";import{r as _}from"./uni-app.es.KXR2Mdtb.js";import{r as v}from"./request.BPygAub8.js";import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const C=w({data:()=>({statusBarHeight:0,password:"",verifyCode:"",counting:0,timer:null,userInfo:{}}),onShow(){this.getUserInfo()},created(){const t=e();this.statusBarHeight=t.statusBarHeight},beforeDestroy(){this.timer&&clearInterval(this.timer)},methods:{async getUserInfo(){try{const e=await v({url:"/api/user/info",method:"GET"});200===e.code&&(this.userInfo=e.data)}catch(e){console.error("获取用户信息失败",e)}},async sendVerifyCode(){if(!(this.counting>0))try{200===(await v({url:"/api/auth/send-reset-code-email",method:"POST",params:{email:this.userInfo.email}})).code&&(t({title:"验证码已发送",icon:"none"}),this.counting=60,this.timer&&clearInterval(this.timer),this.timer=setInterval((()=>{this.counting>0?this.counting--:(clearInterval(this.timer),this.timer=null)}),1e3))}catch(e){t({title:e.message||"发送失败",icon:"none"})}},async handleSubmit(){if(this.password)if(this.verifyCode)try{s({title:"提交中..."});200===(await v({url:"/api/user/security/password/update",method:"POST",data:{securityPassword:this.password,verifyCode:this.verifyCode}})).code&&(t({title:"安全密码修改成功",icon:"success"}),setTimeout((()=>{a()}),1500))}catch(e){t({title:e.message||"修改失败",icon:"none"})}finally{l()}else t({title:"请输入验证码",icon:"none"});else t({title:"请输入安全密码",icon:"none"})},handleBack(){a()},maskEmail(e){if(!e)return"";const[t,s]=e.split("@");return s?t.length<=2?"*".repeat(t.length)+"@"+s:t[0]+"*".repeat(t.length-2)+t[t.length-1]+"@"+s:e}}},[["render",function(e,t,s,a,l,v){const w=_(o("uni-icons"),g),C=r,b=h,I=p,k=y;return c(),i(C,{class:"safepwd-container"},{default:n((()=>[d(C,{class:"custom-navbar",style:f({paddingTop:l.statusBarHeight+"px"})},{default:n((()=>[d(C,{class:"navbar-content"},{default:n((()=>[d(C,{class:"left-area",onClick:v.handleBack},{default:n((()=>[d(w,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),d(b,{class:"page-title"},{default:n((()=>[u("修改安全密码")])),_:1}),d(C,{class:"right-area"})])),_:1})])),_:1},8,["style"]),d(C,{class:"form-content"},{default:n((()=>[d(C,{class:"phone-section"},{default:n((()=>[d(b,{class:"label"},{default:n((()=>[u("当前绑定邮箱：")])),_:1}),d(b,{class:"phone"},{default:n((()=>[u(m(v.maskEmail(l.userInfo.email)),1)])),_:1})])),_:1}),d(C,{class:"form-section"},{default:n((()=>[d(C,{class:"form-item"},{default:n((()=>[d(b,{class:"form-label"},{default:n((()=>[u("安全密码")])),_:1}),d(I,{class:"form-input",type:"password",modelValue:l.password,"onUpdate:modelValue":t[0]||(t[0]=e=>l.password=e),placeholder:"请输入安全密码","placeholder-style":"color: #a18989;",maxlength:"15"},null,8,["modelValue"])])),_:1}),d(C,{class:"form-item"},{default:n((()=>[d(b,{class:"form-label"},{default:n((()=>[u("验证码")])),_:1}),d(C,{class:"verify-group"},{default:n((()=>[d(I,{class:"form-input",type:"text",modelValue:l.verifyCode,"onUpdate:modelValue":t[1]||(t[1]=e=>l.verifyCode=e),placeholder:"验证码","placeholder-style":"color: #a18989;"},null,8,["modelValue"]),d(k,{class:"verify-btn",disabled:l.counting>0,onClick:v.sendVerifyCode},{default:n((()=>[u(m(l.counting>0?`${l.counting}s`:"发送验证码"),1)])),_:1},8,["disabled","onClick"])])),_:1})])),_:1})])),_:1}),d(k,{class:"submit-btn",onClick:v.handleSubmit},{default:n((()=>[u("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-4604d752"]]);export{C as default};
