<template>
  <view class="futures-page">
    <template v-if="loading">
      <!-- 顶部骨架 -->
      <view class="skeleton-header"></view>
      <view class="skeleton-main">
        <view class="skeleton-orderbook"></view>
        <view class="skeleton-trade-panel">
          <view class="skeleton-timer"></view>
          <view class="skeleton-main-price"></view>
          <view class="skeleton-btns"></view>
          <view class="skeleton-input"></view>
          <view class="skeleton-period-list"></view>
          <view class="skeleton-confirm"></view>
        </view>
      </view>
      <view class="skeleton-tabs">
        <view class="skeleton-tab" v-for="i in 3" :key="i">
          <view class="skeleton-tab-header"></view>
          <view class="skeleton-tab-row" v-for="j in 3" :key="j"></view>
        </view>
      </view>
    </template>
    <template v-else>
      <!-- 顶部导航栏 -->
      <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
        <view class="navbar-content">
          <view class="left-area" @click="handleBack">
            <uni-icons type="left" size="20" color="#fff"></uni-icons>
          </view>
          <text class="page-title">带单交易</text>
          <view class="right-area"></view>
        </view>
      </view>

      <!-- 顶部币对和行情 -->
      <view class="futures-header">
        <view style="display: flex; align-items: center; flex: 1;">
          <view class="pair-select" @click="openPairPopup">
            <text class="pair">{{ currentPair ? currentPair.pairName.replace('USDT', '') : 'BTC' }}</text>
            <text style="color:#fff;font-size:10px;margin-left:6rpx;vertical-align:middle;">▼</text>
          </view>
          <view class="trend-rate" style="margin-left: 12rpx;">
            <text
              class="rate"
              :class="Number(changeRate) < 0 ? 'down' : 'up'"
            >
              {{ Number(changeRate) > 0 && !String(changeRate).startsWith('+') && !String(changeRate).startsWith('-') ? '+' : '' }}{{ formatPrice(changeRate) }}%
            </text>
          </view>
        </view>
        <view class="header-btns">
          <!-- K线图按钮已移除 -->
        </view>
      </view>

      <!-- 主体内容 -->
      <view class="futures-main">
        <!-- 盘口 -->
        <view class="orderbook">
          <template v-if="orderbookLoading">
            <view class="orderbook-header">
              <text>价格(USDT)</text>
              <text>数量({{ currentPair ? currentPair.pairName.replace('USDT', '') : 'BTC' }})</text>
            </view>
            <view class="orderbook-list">
              <view class="orderbook-row skeleton-row" v-for="i in 6" :key="'sell-skeleton'+i"></view>
              <view class="orderbook-divider"></view>
              <view class="orderbook-row skeleton-row" v-for="i in 6" :key="'buy-skeleton'+i"></view>
            </view>
          </template>
          <template v-else>
            <view class="orderbook-header">
              <text>价格(USDT)</text>
              <text>数量({{ currentPair ? currentPair.pairName.replace('USDT', '') : 'BTC' }})</text>
            </view>
            <view class="orderbook-list">
              <view class="orderbook-row red" v-for="(item, idx) in sellList.slice(0, 6).reverse()" :key="'sell'+idx">
                <text>{{ formatPrice2(item.price) }}</text>
                <text>{{ formatAmount(item.amount) }}</text>
              </view>
              <view class="orderbook-divider"></view>
              <view class="orderbook-row green" v-for="(item, idx) in buyList.slice(0, 6)" :key="'buy'+idx">
                <text>{{ formatPrice2(item.price) }}</text>
                <text>{{ formatAmount(item.amount) }}</text>
              </view>
            </view>
          </template>
        </view>
        <!-- 操作区 -->
        <view class="trade-panel">
          <view class="trade-price">
            <text
              class="main-price"
              :style="{ color: Number(changeRate) > 0 ? '#FF4D4F' : (Number(changeRate) < 0 ? '#00FF99' : '#fff') }"
            >
              {{ formatPrice(mainPrice) }}
            </text>
            <text
              class="cny"
              :style="{ color: Number(changeRate) > 0 ? '#FF4D4F' : (Number(changeRate) < 0 ? '#00FF99' : '#FFE066') }"
            >
              ≈{{ formatPrice(cnyPrice) }}CNY
            </text>
          </view>
          <view class="trade-btns">
            <view :class="['trade-type', tradeType==='up'?'active':'']" @click="setTradeType('up')">买入开多</view>
            <view :class="['trade-type', tradeType==='down'?'active':'']" @click="setTradeType('down')">卖出开空</view>
          </view>
          
          <view class="trade-amount">
            <view class="leverage-switch-group trade-btns" style="margin-bottom: 18rpx;">
              <view :class="['trade-type', leverage===50 ? 'active' : '']" @click="leverage=50">X50</view>
              <view :class="['trade-type', leverage===100 ? 'active' : '']" @click="leverage=100">X100</view>
            </view>
            <view class="trade-info">
            <text>余额 <span style="color:#fff">{{ copyTradeBalance }}USDT</span></text>
          </view>
            <view style="position: relative; display: flex; align-items: center;">
              <input
                type="number"
                class="amount-input with-suffix"
                :value="amount"
                placeholder="数量"
                style="flex:1; padding-right: 60rpx;"
                @input="onNumberInput('amount', $event)"
                maxlength="10"
              />
              <span class="input-suffix">{{ currentPair ? currentPair.pairName.replace(/(USDT|BTC|ETH|BNB|USD|U)$/i, '') : 'BTC' }}</span>
            </view>
            <view style="display: flex; align-items: center;">
              <text style="color: #fff; font-size: 24rpx; margin-right: 6rpx; width: 70rpx; white-space: nowrap; text-align: left; height: 56rpx; line-height: 56rpx; margin-top: 16rpx;">止盈：</text>
              <input
                type="number"
                class="amount-input"
                :value="takeProfit"
                placeholder="止盈价(可选)"
                style="flex: 1;"
                @input="onNumberInput('takeProfit', $event)"
                maxlength="10"
              />
            </view>
            <view style="display: flex; align-items: center;">
              <text style="color: #fff; font-size: 24rpx; margin-right: 6rpx; width: 70rpx; white-space: nowrap; text-align: left; height: 56rpx; line-height: 56rpx; margin-top: 16rpx;">止损：</text>
              <input
                type="number"
                class="amount-input"
                :value="stopLoss"
                placeholder="止损价(可选)"
                style="flex: 1;"
                @input="onNumberInput('stopLoss', $event)"
                maxlength="10"
              />
            </view>
            <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 16rpx;">
              <text style="color: #fff; font-size: 22rpx;">保证金</text>
              <text style="color: #fff; font-size: 22rpx; font-weight: bold;">
                {{ calculateMargin() }}
              </text>
            </view>
          </view>
          <button class="trade-confirm" @click="onOrderConfirm" :disabled="orderLoading">
            <text v-if="orderLoading">下单中...</text>
            <text v-else>确定</text>
          </button>
        </view>
      </view>

      <!-- 盈利/持仓/成交tab和表格 -->
      <view class="futures-tabs">
        <view class="tab-list">
          <view :class="['tab', activeTab==='hold'?'active':'']" @click="onTabChange('hold')">持仓</view>
          <view :class="['tab', activeTab==='deal'?'active':'']" @click="onTabChange('deal')">成交</view>
          <view :class="['tab', activeTab==='profit'?'active':'']" @click="onTabChange('profit')">盈利</view>
        </view>
        <view v-if="activeTab==='hold'" class="hold-card-list" style="min-height: 700rpx;">
          <!-- 持仓tab的loading状态 -->
          <view v-if="tabLoading.hold && holdList.length === 0" class="tab-loading">
            <text class="loading-text">加载中...</text>
          </view>
          <view v-if="holdList.length === 0 && !tabLoading.hold" class="empty-state">
            <text class="empty-text">暂无持仓数据</text>
          </view>
          <view v-for="(item, idx) in holdList" :key="item.id" class="hold-card">
            <!-- 交易对标题行：币名字 + 杠杆 + 方向 + 一键平仓 -->
            <view class="hold-card-header">
              <view style="display: flex; align-items: center; gap: 8rpx;">
                <text class="hold-symbol">{{ formatSymbol(item.symbol) }}</text>
                <text class="hold-leverage">x{{ item.lever || '--' }}</text>
                <text class="hold-direction" :style="item.direction===1 ? 'background: #02BF87; color: #fff' : 'background: #F34A69; color: #fff'">
                  {{ item.direction===1?'买入开多':'卖出开空' }}
                </text>
              </view>
              <button
                v-if="item.status === 1"
                class="close-position-btn-header"
                @click="closePosition(item)"
                :disabled="item.closing"
                style="margin-right: 12rpx;"
              >
                {{ item.closing ? '平仓中...' : '一键平仓' }}
              </button>
            </view>
            <!-- 第一行：数量、保证金、开仓价格 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">数量</text>
                <text class="hold-value-compact">{{ item.positionAmount }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">保证金</text>
                <text class="hold-value-compact">{{ formatMargin(item.marginAmount) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">开仓价</text>
                <text class="hold-value-compact">{{ item.openPrice }}</text>
              </view>
            </view>
            <!-- 第二行：盈利、收益率、状态 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">盈利</text>
                <text class="hold-value-compact" :class="calculateRealTimeProfit(item) > 0 ? 'green' : 'red'">{{ calculateRealTimeProfit(item).toFixed(3) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">收益率</text>
                <text class="hold-value-compact" :class="calculateRealTimeProfit(item) > 0 ? 'green' : 'red'">{{ calculateProfitRate(item) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">状态</text>
                <text class="hold-value-compact">
                  {{ item.status === 1 ? '持仓中' : item.status === 2 ? '已平仓' : '--' }}
                </text>
              </view>
            </view>
            <!-- 第三行：止盈、止损、时间 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">止盈</text>
                <text class="hold-value-compact">{{ formatPrice(item.takeProfit) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">止损</text>
                <text class="hold-value-compact">{{ formatPrice(item.stopLoss) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">时间</text>
                <text class="hold-value-compact hold-time-compact">{{ formatTime(item.openTime) }}</text>
              </view>
            </view>
          </view>
        </view>
        <view v-if="activeTab==='deal'" class="hold-card-list" style="min-height: 700rpx;">
          <!-- 成交tab的loading状态 -->
          <view v-if="tabLoading.deal && dealList.length === 0" class="tab-loading">
            <text class="loading-text">加载中...</text>
          </view>
          <view v-if="dealList.length === 0 && !tabLoading.deal" class="empty-state">
            <text class="empty-text">暂无成交数据</text>
          </view>
          <view v-for="item in dealList" :key="item.id" class="hold-card">
            <!-- 交易对标题行：币名字 + 杠杆（左边） + 方向（右边） -->
            <view class="hold-card-header">
              <view style="display: flex; align-items: center; gap: 8rpx;">
                <text class="hold-symbol">{{ formatSymbol(item.symbol) }}</text>
                <text class="hold-leverage">x{{ item.lever || '--' }}</text>
              </view>
              <text class="hold-direction" :style="item.direction===1 ? 'background: #02BF87; color: #fff' : 'background: #F34A69; color: #fff'">
                {{ item.direction===1?'买入开多':'卖出开空' }}
              </text>
            </view>
            <!-- 第一行：数量、保证金、开仓价格 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">数量</text>
                <text class="hold-value-compact">{{ item.positionAmount || '--' }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">保证金</text>
                <text class="hold-value-compact">{{ formatMargin(item.marginAmount) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">开仓价</text>
                <text class="hold-value-compact">{{ item.openPrice || '--' }}</text>
              </view>
            </view>
            <!-- 第二行：盈亏、收益率、状态 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">盈亏</text>
                <text class="hold-value-compact" :class="item.profit > 0 ? 'green' : 'red'">{{ item.profit || '--' }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">收益率</text>
                <text class="hold-value-compact" :class="item.profit > 0 ? 'green' : 'red'">{{ formatDealProfitRate(item) }}</text>
              </view>
             <view class="hold-col-compact">
               <text class="hold-label-compact">平仓价</text>
               <text class="hold-value-compact">{{ item.closePrice || '--' }}</text>
             </view>
            </view>
            <!-- 第三行：止盈、止损、时间 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">止盈</text>
                <text class="hold-value-compact">{{ formatPrice(item.takeProfit) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">止损</text>
                <text class="hold-value-compact">{{ formatPrice(item.stopLoss) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">时间</text>
                <text class="hold-value-compact hold-time-compact">{{ formatTime(item.closeTime) }}</text>
              </view>
            </view>
          </view>
          <view v-if="dealList.length > 0" class="table-more">
            <button v-if="dealHasMore" @click="onDealLoadMore" :disabled="tabLoading.deal">加载更多</button>
            <text v-else style="color:#fff !important;font-size:22rpx;">已经没有了</text>
          </view>
        </view>
        <view v-if="activeTab==='profit'" class="hold-card-list" style="min-height: 700rpx;">
          <!-- 盈利tab的loading状态 -->
          <view v-if="tabLoading.profit && profitList.length === 0" class="tab-loading">
            <text class="loading-text">加载中...</text>
          </view>
          <view v-if="profitList.length === 0 && !tabLoading.profit" class="empty-state">
            <text class="empty-text">暂无盈利数据</text>
          </view>
          <view v-for="item in profitList" :key="item.id" class="hold-card">
            <!-- 交易对标题行：币名字 + 杠杆（左边） + 方向（右边） -->
            <view class="hold-card-header">
              <view style="display: flex; align-items: center; gap: 8rpx;">
                <text class="hold-symbol">{{ formatSymbol(item.symbol) }}</text>
                <text class="hold-leverage">x{{ item.lever || '--' }}</text>
              </view>
              <text class="hold-direction" :style="item.direction===1 ? 'background: #02BF87; color: #fff' : 'background: #F34A69; color: #fff'">
                {{ item.direction===1?'买入开多':'卖出开空' }}
              </text>
            </view>
            <!-- 第一行：数量、保证金、开仓价格 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">数量</text>
                <text class="hold-value-compact">{{ item.positionAmount || '--' }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">保证金</text>
                <text class="hold-value-compact">{{ formatMargin(item.marginAmount) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">开仓价</text>
                <text class="hold-value-compact">{{ item.openPrice || '--' }}</text>
              </view>
            </view>
            <!-- 第二行：获利、收益率、状态 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">获利</text>
                <text class="hold-value-compact" :class="item.profit > 0 ? 'green' : 'red'">{{ item.profit || '--' }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">收益率</text>
                <text class="hold-value-compact" :class="item.profit > 0 ? 'green' : 'red'">{{ formatDealProfitRate(item) }}</text>
              </view>
             <view class="hold-col-compact">
               <text class="hold-label-compact">平仓价</text>
               <text class="hold-value-compact">{{ item.closePrice || '--' }}</text>
             </view>
            </view>
            <!-- 第三行：止盈、止损、时间 -->
            <view class="hold-row-3col">
              <view class="hold-col-compact">
                <text class="hold-label-compact">止盈</text>
                <text class="hold-value-compact">{{ formatPrice(item.takeProfit) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">止损</text>
                <text class="hold-value-compact">{{ formatPrice(item.stopLoss) }}</text>
              </view>
              <view class="hold-col-compact">
                <text class="hold-label-compact">时间</text>
                <text class="hold-value-compact hold-time-compact">{{ formatTime(item.closeTime) }}</text>
              </view>
            </view>
          </view>
          <view v-if="profitList.length > 0" class="table-more">
            <button v-if="profitHasMore" @click="onProfitLoadMore" :disabled="tabLoading.profit">加载更多</button>
            <text v-else style="color:#fff !important;font-size:22rpx;">已经没有了</text>
          </view>
        </view>
        <!-- 其他tab可后续补充 -->
      </view>

      <uni-popup ref="pairPopup" type="bottom" :mask-click="true">
        <view class="pair-popup">
          <view class="pair-popup-title">选择币对</view>
          <view class="pair-popup-list">
            <view v-for="item in trendList" :key="item.pairName" class="pair-popup-item" :class="{active: currentPair && currentPair.pairName === item.pairName}" @click="selectPair(item)">
              <image :src="getImageUrl(item.logoUrl)" class="pair-popup-logo" mode="aspectFit" />
              <view class="pair-popup-info">
                <text class="pair-popup-name">{{ item.pairName.replace('USDT', '') }}</text>
                <text class="pair-popup-token">{{ item.tokenName }}</text>
              </view>
              <view class="pair-popup-price">
                <text class="pair-price">{{ formatPairPrice(item.price) }}</text>
                <text
                  class="pair-change"
                  :class="Number(item.change) > 0 ? 'rise' : 'fall'"
                >
                  {{ Number(item.change) > 0 ? '+' : '-' }}{{ Math.abs(Number(item.change)).toFixed(2) }}%
                </text>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
    </template>
  </view>
</template>

<script>
import config from '@/config/index.js'
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 20, // 状态栏高度
      baseURL: config.apiBaseUrl,
      activeTab: 'hold',
      activePeriod: 60,
      tradeType: 'up',
      mainPrice: '--',
      cnyPrice: '--',
      changeRate: '--',
      eventSource: null,
      sellList: [],
      buyList: [],
      profitList: [],
      profitPage: 1,
      pageSize: 10,
      profitTotal: 0,
      loading: false,
      profitHasMore: true,
      holdList: [],
      holdPage: 1,
      holdTotal: 0,
      holdHasMore: true,
      dealList: [],
      dealPage: 1,
      dealTotal: 0,
      dealHasMore: true,
      // showPairPopup: false, // 移除
      trendList: [],
      currentPair: null,
      eventSourceTrend: null,
      currentTime: '', // 新增字段
      copyTradeBalance: 0, // 带单账余额
      amount: '', // 新增字段
      orderLoading: false, // 新增字段
      holdProfitSSE: null, // 新增字段
      orderbookLoading: true, // 新增字段
      leverage: 50, // 新增杠杆倍数
      takeProfit: '', // 止盈价
      stopLoss: '', // 止损价
      // 新增：记录各个tab是否已经加载过数据
      tabDataLoaded: {
        hold: false,
        deal: false,
        profit: false
      },
      // 新增：各个tab的独立loading状态
      tabLoading: {
        hold: false,
        deal: false,
        profit: false
      }
    }
  },

  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },

  mounted() {
    this.loadAllData();
    this.setupSSE();
    this.initTrendSSE();
    this.loadUserInfo(); // 新增
    this.initHoldProfitSSE(); // 新增
    // 不再自动初始化止盈止损，保持为空让用户选择输入
  },
  beforeDestroy() { // 新增生命周期钩子
    if (this.holdProfitSSE) {
      this.holdProfitSSE.close();
      this.holdProfitSSE = null;
    }
  },
  methods: {
    handleBack() {
      // 返回到我的页面
      uni.switchTab({ url: '/pages/mine/index' })
    },
    // 设置交易类型
    setTradeType(type) {
      this.tradeType = type;
      // 不再自动更新止盈止损，保持用户输入
    },
    // 自动更新止盈止损价格（仅在字段为空时）
    updateTakeProfitStopLoss() {
      // 检查是否有有效的实时价格
      if (!this.mainPrice || this.mainPrice === '--' || isNaN(this.mainPrice)) {
        return;
      }

      const currentPrice = Number(this.mainPrice);

      // 只有在字段为空时才自动填充
      if (!this.takeProfit || this.takeProfit === '') {
        if (this.tradeType === 'up') {
          // 买涨：止盈 = 当前价格 + 1000
          this.takeProfit = (currentPrice + 1000).toFixed(2);
        } else {
          // 买跌：止盈 = 当前价格 - 1000
          this.takeProfit = (currentPrice - 1000).toFixed(2);
        }
      }

      if (!this.stopLoss || this.stopLoss === '') {
        if (this.tradeType === 'up') {
          // 买涨：止损 = 当前价格 - 500
          this.stopLoss = (currentPrice - 500).toFixed(2);
        } else {
          // 买跌：止损 = 当前价格 + 500
          this.stopLoss = (currentPrice + 500).toFixed(2);
        }
      }
    },

    // 计算默认止盈止损价格（用于提交时的默认值）
    calculateDefaultTakeProfitStopLoss() {
      if (!this.mainPrice || this.mainPrice === '--' || isNaN(this.mainPrice)) {
        return { takeProfit: null, stopLoss: null };
      }

      const currentPrice = Number(this.mainPrice);
      let defaultTakeProfit, defaultStopLoss;

      if (this.tradeType === 'up') {
        // 买涨：止盈 = 当前价格 + 1000，止损 = 当前价格 - 500
        defaultTakeProfit = currentPrice + 1000;
        defaultStopLoss = currentPrice - 500;
      } else {
        // 买跌：止盈 = 当前价格 - 1000，止损 = 当前价格 + 500
        defaultTakeProfit = currentPrice - 1000;
        defaultStopLoss = currentPrice + 500;
      }

      return { takeProfit: defaultTakeProfit, stopLoss: defaultStopLoss };
    },
    // 计算保证金：购买的数量 * 实时价格 / 杠杆倍数
    calculateMargin() {
      if (!this.amount || isNaN(this.amount) || !this.leverage || !this.mainPrice || isNaN(this.mainPrice)) {
        return '--';
      }

      const quantity = Number(this.amount);        // 购买数量
      const currentPrice = Number(this.mainPrice); // 实时价格
      const leverageRatio = Number(this.leverage); // 杠杆倍数

      // 保证金 = 数量 * 实时价格 / 杠杆倍数
      const margin = (quantity * currentPrice) / leverageRatio;

      return margin.toFixed(2);
    },
    // 计算保证金数值（返回数字，用于验证）
    calculateMarginValue() {
      if (!this.amount || isNaN(this.amount) || !this.leverage || !this.mainPrice || isNaN(this.mainPrice)) {
        return null;
      }

      const quantity = Number(this.amount);        // 购买数量
      const currentPrice = Number(this.mainPrice); // 实时价格
      const leverageRatio = Number(this.leverage); // 杠杆倍数

      // 保证金 = 数量 * 实时价格 / 杠杆倍数
      return (quantity * currentPrice) / leverageRatio;
    },
    // 计算实时盈利
    calculateRealTimeProfit(item) {
      // 优先使用实时推送的数据
      if (item.realTimeProfit !== undefined) {
        return Number(item.realTimeProfit);
      }

      // 如果没有实时推送数据，则使用当前价格计算
      if (!this.mainPrice || this.mainPrice === '--' || isNaN(this.mainPrice)) {
        return Number(item.profit) || 0;
      }

      const currentPrice = Number(this.mainPrice);
      const openPrice = Number(item.openPrice);
      const quantity = Number(item.positionAmount);

      if (!openPrice || !quantity) {
        return Number(item.profit) || 0;
      }

      let profit = 0;
      if (item.direction === 1) {
        // 买涨：盈利 = (当前价格 - 开仓价格) × 数量
        profit = (currentPrice - openPrice) * quantity;
      } else {
        // 买跌：盈利 = (开仓价格 - 当前价格) × 数量
        profit = (openPrice - currentPrice) * quantity;
      }

      return profit;
    },
    // 计算收益率
    calculateProfitRate(item) {
      // 优先使用实时推送的收益率
      if (item.realTimeProfitRate !== undefined) {
        return Number(item.realTimeProfitRate).toFixed(3) + '%';
      }

      // 如果没有实时推送数据，则计算
      const realTimeProfit = this.calculateRealTimeProfit(item);
      if (!item.marginAmount || item.marginAmount === 0) {
        return '--';
      }
      const rate = (realTimeProfit / Number(item.marginAmount)) * 100;
      return rate.toFixed(3) + '%';
    },
    // 格式化成交和盈利的收益率（静态数据，不动态更新）
    formatDealProfitRate(item) {
      if (!item.profit || !item.marginAmount || item.marginAmount === 0) {
        return '--';
      }
      const rate = (Number(item.profit) / Number(item.marginAmount)) * 100;
      return rate.toFixed(3) + '%';
    },

    // 格式化保证金显示
    formatMargin(marginAmount) {
      if (!marginAmount || marginAmount === 0) {
        return '--';
      }
      const margin = Number(marginAmount);
      if (isNaN(margin)) {
        return '--';
      }
      return margin.toFixed(2);
    },

    // 更新持仓列表的实时盈利（强制重新渲染）
    updateHoldListProfit() {
      if (this.activeTab === 'hold' && this.holdList.length > 0) {
        // 通过修改数组引用来触发Vue的响应式更新
        this.holdList = [...this.holdList];
      }
    },
    // 一键平仓
    async closePosition(item) {
      try {
        // 确认对话框
        const result = await new Promise((resolve) => {
          uni.showModal({
            title: '确认平仓',
            content: `确定要平仓这个${item.direction === 1 ? '买涨' : '买跌'}订单吗？\n数量：${item.positionAmount}\n保证金：${this.formatMargin(item.marginAmount)}\n当前盈利：${this.calculateRealTimeProfit(item).toFixed(3)}`,
            confirmText: '确认平仓',
            cancelText: '取消',
            success: (res) => {
              resolve(res.confirm);
            }
          });
        });

        if (!result) {
          return; // 用户取消
        }

        // 设置平仓中状态
        this.$set(item, 'closing', true);

        // 调用平仓接口
        const res = await request({
          url: '/api/copy/order/close',
          method: 'POST',
          data: {
            orderId: item.id
          }
        });

        if (res.code === 200) {
          uni.showToast({
            title: '平仓成功',
            icon: 'success',
            duration: 2000
          });

          // 从持仓列表中移除
          const index = this.holdList.findIndex(o => o.id === item.id);
          if (index !== -1) {
            this.holdList.splice(index, 1);
          }

          // 刷新余额
          await this.loadUserInfo();

        } else {
          uni.showToast({
            title: res.message || res.msg || '平仓失败',
            icon: 'none',
            duration: 3000
          });
        }

      } catch (error) {
        console.error('平仓失败:', error);
        uni.showToast({
          title: error.message || '平仓失败，请重试',
          icon: 'none',
          duration: 3000
        });
      } finally {
        // 重置平仓中状态
        this.$set(item, 'closing', false);
      }
    },
    getImageUrl(imageUrl) {
      if (!imageUrl) return '';
      if (imageUrl.startsWith('/static/')) {
        return imageUrl;
      }
      if (imageUrl.startsWith('/')) {
        return `${this.baseURL}${imageUrl}`;
      }
      return imageUrl;
    },
    formatPrice(val) {
      if (val === undefined || val === null || val === '' || isNaN(val)) return '--';
      let num = Number(val);
      if (num === 0) return '0';
      let str = num.toString();
      if (str.indexOf('.') > -1) {
        str = str.replace(/(\.\d*?[1-9])0+$/, '$1').replace(/\.0+$/, '');
      }
      return str;
    },
    formatPrice2(val) {
      if (val === undefined || val === null || val === '' || isNaN(val)) return '--';
      return Number(val).toFixed(2);
    },
    formatAmount(val) {
      if (val === undefined || val === null || val === '' || isNaN(val)) return '--';
      let num = Number(val);
      if (num === 0) return '0';
      if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
      if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
      if (num >= 1e3) return (num / 1e3).toFixed(2) + 'k';
      let str = num.toString();
      if (str.length > 6) str = num.toFixed(4);
      return str;
    },
    formatTime(val) {
      if (!val) return '';
      let date;
      if (typeof val === 'string' && val.length >= 19) {
        date = new Date(val);
      } else if (typeof val === 'number' || (typeof val === 'string' && /^\d+$/.test(val))) {
        let num = Number(val);
        if (val.length === 10) {
          date = new Date(num * 1000); // 秒时间戳
        } else if (val.length === 13) {
          date = new Date(num); // 毫秒时间戳
        } else {
          return '--';
        }
      } else {
        return '--';
      }
      const pad = n => n < 10 ? '0' + n : n;
      const month = pad(date.getMonth() + 1);
      const day = pad(date.getDate());
      const h = pad(date.getHours());
      const m = pad(date.getMinutes());
      const s = pad(date.getSeconds());
      return `${month}-${day} ${h}:${m}:${s}`;
    },
    formatSymbol(symbol) {
      if (!symbol) return '--';
      // 移除 USDT 后缀，显示更简洁的交易对名称
      return symbol.replace('/USDT', '').replace('USDT', '');
    },
    formatPairPrice(val) {
      if (val === undefined || val === null || val === '' || isNaN(val)) return '--';
      const num = Number(val);
      if (num >= 1) {
        return num.toFixed(2);
      } else {
        return num.toFixed(5).replace(/0+$/, '').replace(/\.$/, '');
      }
    },
    setupSSE() {
      if (this.eventSource) this.eventSource.close();
      const token = uni.getStorageSync('token') || '';
      if (!token) return;
      this.eventSource = new window.EventSource(
        this.baseURL + '/api/market/futures/stream?token=' + encodeURIComponent(token) + '&symbol=' + encodeURIComponent(this.currentPair)
      );
      this.eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'orderbook' || data.type === 'futures_update') {
          this.buyList = data.buyList || [];
          this.sellList = data.sellList || [];
          this.orderbookLoading = false;
        }
        if (data.type === 'ticker' || data.type === 'futures_update') {
          this.mainPrice = data.price || '--';
          this.cnyPrice = data.cnyPrice || '--';
          this.changeRate = data.changeRate || '--';
          // 不再自动更新止盈止损价格，保持用户输入
          // 触发持仓列表的实时盈利更新
          this.updateHoldListProfit();
        }
      };
      this.eventSource.onerror = () => {
        this.eventSource.close();
        setTimeout(() => this.setupSSE(), 5000);
      };
    },
    initTrendSSE() {
      if (this.eventSourceTrend) this.eventSourceTrend.close();
      const token = uni.getStorageSync('token') || '';
      if (!token) return;
      this.eventSourceTrend = new window.EventSource(
        this.baseURL + '/api/market/trend/stream?token=' + encodeURIComponent(token)
      );
      this.eventSourceTrend.onmessage = (event) => {
        let data = event.data;
        if (data.startsWith('data: ')) data = data.slice(6);
        try {
          const arr = JSON.parse(data);
          arr.forEach(item => {
            item.cnyPrice = (Number(item.price) * 7).toFixed(2);
          });
          this.trendList = arr;
          // 如果未选中币对，默认第一个
          if (!this.currentPair && arr.length > 0) {
            this.currentPair = arr[0];
            this.switchPair(arr[0]);
          }
        } catch (e) {}
      };
      this.eventSourceTrend.onerror = () => {
        this.eventSourceTrend.close();
      };
    },
    selectPair(item) {
      this.currentPair = item;
      if (this.$refs.pairPopup && this.$refs.pairPopup.close) {
        this.$refs.pairPopup.close();
      }
      this.switchPair(item);
    },
    switchPair(item) {
      // 切换币对后重建主行情SSE
      if (this.eventSource) this.eventSource.close();
      const token = uni.getStorageSync('token') || '';
      if (!token) return;
      this.eventSource = new window.EventSource(
        this.baseURL + '/api/market/futures/stream?symbol=' + encodeURIComponent(item.pairName) + '&token=' + encodeURIComponent(token)
      );
      this.eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'orderbook' || data.type === 'futures_update') {
          this.buyList = data.buyList || [];
          this.sellList = data.sellList || [];
          this.orderbookLoading = false;
        }
        if (data.type === 'ticker' || data.type === 'futures_update') {
          this.mainPrice = data.price || '--';
          this.cnyPrice = data.cnyPrice || '--';
          this.changeRate = data.changeRate || '--';
          // 不再自动更新止盈止损价格，保持用户输入
          // 触发持仓列表的实时盈利更新
          this.updateHoldListProfit();
        }
      };
      this.eventSource.onerror = () => {
        this.eventSource.close();
        setTimeout(() => this.switchPair(item), 5000);
      };
    },

    openPairPopup() {
      this.$refs.pairPopup && this.$refs.pairPopup.open();
    },
    async loadProfitList(isLoadMore = false) {
      this.tabLoading.profit = true;
      try {
        const res = await request({
          url: '/api/copy/order/profit',
          method: 'GET',
          params: { page: this.profitPage, pageSize: this.pageSize }
        });
        if (res.code === 200) {
          const records = res.data.list || [];
          if (isLoadMore) {
            this.profitList = this.profitList.concat(records);
          } else {
            this.profitList = records;
          }
          this.profitTotal = res.data.total;
          this.profitHasMore = res.data.hasMore;
        }
      } finally {
        this.tabLoading.profit = false;
      }
    },
    async loadHoldList(isLoadMore = false) {
      this.tabLoading.hold = true;
      try {
        const res = await request({
          url: '/api/copy/order/hold',
          method: 'GET',
          params: { page: this.holdPage, pageSize: this.pageSize }
        });
        if (res.code === 200) {
          const records = res.data.list || [];
          if (isLoadMore) {
            this.holdList = this.holdList.concat(records);
          } else {
            this.holdList = records;
          }
          this.holdTotal = res.data.total;
          this.holdHasMore = res.data.hasMore;
        }
      } finally {
        this.tabLoading.hold = false;
      }
    },
    async loadDealList(isLoadMore = false) {
      this.tabLoading.deal = true;
      try {
        const res = await request({
          url: '/api/copy/order/deal',
          method: 'GET',
          params: { page: this.dealPage, pageSize: this.pageSize }
        });
        if (res.code === 200) {
          const records = res.data.list || [];
          if (isLoadMore) {
            this.dealList = this.dealList.concat(records);
          } else {
            this.dealList = records;
          }
          this.dealTotal = res.data.total;
          this.dealHasMore = res.data.hasMore;
        }
      } finally {
        this.tabLoading.deal = false;
      }
    },
    async loadUserInfo() { // 新增方法
      try {
        const res = await request({ url: '/api/user/info', method: 'GET' });
        if (res.code === 200 && res.data) {
          this.copyTradeBalance = res.data.copyTradeBalance;
        }
      } catch (e) {}
    },
    async onOrderConfirm() { // 新增方法
      if (this.orderLoading) return;

      // 验证数量输入
      if (!this.amount || isNaN(this.amount)) {
        uni.showToast({ title: '请输入正确的数量', icon: 'none' });
        return;
      }

      const quantity = Number(this.amount);
      if (quantity <= 0) {
        uni.showToast({ title: '数量必须大于0', icon: 'none' });
        return;
      }

      // 验证实时价格
      if (!this.mainPrice || isNaN(this.mainPrice)) {
        uni.showToast({ title: '获取价格失败，请稍后重试', icon: 'none' });
        return;
      }

      // 计算所需保证金
      const requiredMargin = this.calculateMarginValue();
      if (requiredMargin === null) {
        uni.showToast({ title: '计算保证金失败，请检查输入', icon: 'none' });
        return;
      }

      // 验证保证金是否充足
      if (requiredMargin > Number(this.copyTradeBalance)) {
        uni.showToast({
          title: `保证金不足，需要${requiredMargin.toFixed(2)}USDT，余额${Number(this.copyTradeBalance).toFixed(2)}USDT`,
          icon: 'none',
          duration: 3000
        });
        return;
      }

      // 获取止盈止损价格（如果用户没有输入则使用默认值）
      const defaults = this.calculateDefaultTakeProfitStopLoss();
      let takeProfitPrice, stopLossPrice;

      if (this.takeProfit && this.takeProfit !== '' && !isNaN(this.takeProfit)) {
        takeProfitPrice = Number(this.takeProfit);
      } else {
        takeProfitPrice = defaults.takeProfit;
      }

      if (this.stopLoss && this.stopLoss !== '' && !isNaN(this.stopLoss)) {
        stopLossPrice = Number(this.stopLoss);
      } else {
        stopLossPrice = defaults.stopLoss;
      }

      // 验证默认值是否有效
      if (!takeProfitPrice || !stopLossPrice) {
        uni.showToast({ title: '无法获取当前价格，请稍后重试', icon: 'none' });
        return;
      }

      // 验证止盈止损价格合理性
      const currentPrice = Number(this.mainPrice);

      if (this.tradeType === 'up') { // 买涨
        if (takeProfitPrice <= currentPrice) {
          uni.showToast({ title: '买涨时止盈价格必须大于当前价格', icon: 'none' });
          return;
        }
        if (stopLossPrice >= currentPrice) {
          uni.showToast({ title: '买涨时止损价格必须小于当前价格', icon: 'none' });
          return;
        }
      } else { // 买跌
        if (takeProfitPrice >= currentPrice) {
          uni.showToast({ title: '买跌时止盈价格必须小于当前价格', icon: 'none' });
          return;
        }
        if (stopLossPrice <= currentPrice) {
          uni.showToast({ title: '买跌时止损价格必须大于当前价格', icon: 'none' });
          return;
        }
      }

      // 验证币对
      if (!this.currentPair || !this.currentPair.pairName) {
        uni.showToast({ title: '请选择币对', icon: 'none' });
        return;
      }
      this.orderLoading = true;
      try {
        const res = await request({
          url: '/api/copy/order/create',
          method: 'POST',
          data: {
            symbol: this.currentPair.pairName,
            direction: this.tradeType === 'up' ? 1 : 2, // 1:买涨, 2:买跌
            leverage: this.leverage,
            quantity: Number(this.amount),
            takeProfit: takeProfitPrice,
            stopLoss: stopLossPrice,
          }
        });
        if (res.code === 200) {
          uni.showToast({ title: '下单成功', icon: 'success' });
          this.amount = '';
          // 清空止盈止损字段，让用户重新选择
          this.takeProfit = '';
          this.stopLoss = '';
          await this.loadUserInfo(); // 确保余额刷新
          this.holdPage = 1;
          this.loadHoldList(false);
        } else {
          uni.showToast({ title: res.message || res.msg || '下单失败', icon: 'none' });
        }
      } catch (e) {
        console.error('下单异常:', e);
        uni.showToast({ title: e.message || '下单异常', icon: 'none' });
      } finally {
        this.orderLoading = false;
      }
    },
    onProfitLoadMore() {
      if (this.tabLoading.profit || !this.profitHasMore) return;
      this.profitPage++;
      this.loadProfitList(true);
    },
    onHoldLoadMore() {
      if (this.tabLoading.hold || !this.holdHasMore) return;
      this.holdPage++;
      this.loadHoldList(true);
    },
    onDealLoadMore() {
      if (this.tabLoading.deal || !this.dealHasMore) return;
      this.dealPage++;
      this.loadDealList(true);
    },
    onTabChange(tab) {
      this.activeTab = tab;
      // 只在首次切换到该tab时才加载数据
      if (!this.tabDataLoaded[tab]) {
        if (tab === 'profit') {
          this.profitPage = 1;
          this.loadProfitList(false);
          this.tabDataLoaded.profit = true;
        } else if (tab === 'hold') {
          this.holdPage = 1;
          this.loadHoldList(false);
          this.tabDataLoaded.hold = true;
        } else if (tab === 'deal') {
          this.dealPage = 1;
          this.loadDealList(false);
          this.tabDataLoaded.deal = true;
        }
      }
    },
    initHoldProfitSSE() { // 新增方法
      if (this.holdProfitSSE) this.holdProfitSSE.close();
      const token = uni.getStorageSync('token') || '';
      if (!token) {
        console.log('Leader页面：没有token，无法建立SSE连接');
        return;
      }

      const sseUrl = this.baseURL + '/api/copy/order/hold/profit/stream?token=' + encodeURIComponent(token);
      console.log('Leader页面：建立SSE连接:', sseUrl);

      this.holdProfitSSE = new window.EventSource(sseUrl);

      this.holdProfitSSE.onopen = () => {
        console.log('Leader页面：SSE连接已建立');
      };

      this.holdProfitSSE.onerror = (error) => {
        console.error('Leader页面：SSE连接错误:', error);
        this.holdProfitSSE.close();
        setTimeout(() => this.initHoldProfitSSE(), 5000);
      };

      // 监听 profit-update 事件
      this.holdProfitSSE.addEventListener('profit-update', async (event) => {
        console.log('Leader页面：收到SSE原始事件:', event.data);
        let data = event.data;
        if (data.startsWith('data: ')) data = data.slice(6);
        try {
          const arr = JSON.parse(data);
          console.log('Leader页面：解析后的持仓盈利数据:', arr);
          let removed = false;
          let removedCount = 0;

          arr.forEach(item => {
            const idx = this.holdList.findIndex(o => o.id === item.orderId);

            if (item.status === 2) {
              // 已平仓状态，从持仓列表中移除
              if (idx !== -1) {
                this.holdList.splice(idx, 1);
                removed = true;
                removedCount++;
                console.log(`订单${item.orderId}已平仓，从持仓列表中移除`);
              }
            } else if (item.status === 1 && idx !== -1) {
              // 持仓中状态，更新实时盈利数据
              this.holdList[idx].realTimeProfit = item.profit;
              this.holdList[idx].realTimeProfitRate = item.profitRate;
              this.holdList[idx].currentPrice = item.currentPrice;
              this.holdList[idx].status = item.status;
            }
          });

          if (removed) {
            console.log(`共移除${removedCount}个已平仓订单`);
            // 显示用户提示
            if (removedCount === 1) {
              uni.showToast({
                title: '订单已平仓',
                icon: 'success',
                duration: 2000
              });
            } else if (removedCount > 1) {
              uni.showToast({
                title: `${removedCount}个订单已平仓`,
                icon: 'success',
                duration: 2000
              });
            }
            await this.loadUserInfo(); // 刷新余额
            // 如果当前在持仓页面，强制刷新显示
            if (this.activeTab === 'hold') {
              this.$forceUpdate();
            }
          }
        } catch (e) {
          console.error('Leader页面：处理实时盈利数据失败:', e);
        }
      });
    },
    async loadAllData() {
      this.loading = true;
      // 默认加载持仓数据（因为默认activeTab是'hold'）
      if (this.activeTab === 'hold' && !this.tabDataLoaded.hold) {
        await this.loadHoldList(false);
        this.tabDataLoaded.hold = true;
      }
      this.loading = false;
    },
    onNumberInput(field, e) {
      let val = e.detail.value;
      // 只允许数字和一个小数点
      val = val.replace(/[^\d.]/g, '')
               .replace(/\.(?=.*\.)/g, '');
      // 去除开头多余的0
      if(val.startsWith('00')) val = val.replace(/^0+/, '0');
      // 小数点后只保留6位
      if(val.indexOf('.') > -1) val = val.replace(/(\.\d{0,6}).*$/, '$1');
      this[field] = val;
    },
    // 新增：强制刷新当前tab数据的方法
    refreshCurrentTab() {
      const tab = this.activeTab;
      if (tab === 'profit') {
        this.profitPage = 1;
        this.loadProfitList(false);
      } else if (tab === 'hold') {
        this.holdPage = 1;
        this.loadHoldList(false);
      } else if (tab === 'deal') {
        this.dealPage = 1;
        this.loadDealList(false);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #111111;
  backdrop-filter: blur(10px);

  .navbar-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;

    .left-area {
      width: 80rpx;
      height: 44px;
      display: flex;
      align-items: center;

      &:active {
        opacity: 0.7;
      }
    }

    .page-title {
      color: #fff;
      font-size: 30rpx !important;
      font-weight: 500;
    }

    .right-area {
      width: 80rpx;
    }
  }
}

.futures-page {
  min-height: 100vh;
  background: #121212 !important;
  padding-bottom: 100rpx;
  padding-top: calc(var(--status-bar-height) + 44px);
}
.futures-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 16rpx 0 16rpx;
  background: #121212 !important;
  border-radius: 10rpx;
  .pair-select {
    display: flex;
    align-items: center;
    .pair {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
      margin-right: 6rpx;
      text-shadow: 0 2rpx 8rpx #FFD70044;
    }
  }
  .trend-rate {
    display: flex;
    align-items: center;
    background: rgba(255,106,0,0.10);
    border-radius: 10rpx;
    padding: 0 10rpx;
    height: 28rpx;
    .rate {
      color: #FF6A00;
      font-size: 18rpx;
      margin-left: 2rpx;
      font-weight: 500;
    }
  }
  .header-btns {
    display: flex;
    align-items: center;
    .header-btn {
      width: 36rpx;
      height: 36rpx;
      background: #181818;
      // border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8rpx;
      // border: 1px solid #FFD70088;
      // box-shadow: 0 2rpx 8rpx #FFD70022;
    }
  }
}
.futures-main {
  display: flex;
  flex-direction: row;
  padding: 0 24rpx;
  margin-top: 12rpx;
  gap: 16rpx;
  // background: #18191D !important;
  border-radius: 10rpx;
}
.orderbook {
  width: 50%;
  background: #18191D !important;
  border-radius: 10rpx;
  border: 1px solid #FFD70033;
  margin-right: 0;
  padding: 10rpx 0 10rpx 0;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  .orderbook-header {
    display: flex;
    justify-content: space-between;
    color: #fff;
    font-size: 20rpx;
    font-weight: 600;
    padding: 0 12rpx 6rpx 12rpx;
    letter-spacing: 0.5px;
  }
  .orderbook-list {
    .orderbook-row {
      display: flex;
      justify-content: space-between;
      padding: 2rpx 12rpx;
      font-size: 22rpx;
      color: #fff;
      border-radius: 4rpx;
      transition: background 0.2s;
      min-height: 32rpx;
      margin-bottom: 15rpx;
      &.red {
        color: #FF6A00;
      }
      &.green {
        color: #00FF99;
      }
      &:hover {
        background: #FFD70011;
      }
    }
    .orderbook-row.red text {
      color: #F34A69 !important;
    }
    .orderbook-row.green text {
      color: #02BF87 !important;
    }
    .orderbook-divider {
      height: 1px;
      background: linear-gradient(90deg, #FFD70033 0%, #FFD70099 50%, #FFD70033 100%);
      margin: 8rpx 0;
      width: 90%;
      align-self: center;
      border-radius: 1px;
    }
  }
}
.trade-panel {
  width: 50%;
  background: #18191D !important;
  border-radius: 24rpx;
  border: 1px solid #FFD700;
  padding: 16rpx 16rpx 16rpx 16rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 16rpx #FFD70011;
  .trade-timer {
    display: flex;
    align-items: center;
    background: #FFD700;
    border-radius: 12rpx;
    padding: 0 18rpx;
    height: 40rpx;
    margin-bottom: 28rpx;
    .timer {
      color: #222;
      font-size: 24rpx;
      margin-left: 10rpx;
      font-weight: bold;
    }
  }
  .trade-price {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 40rpx;
    color: #FFD700;
    font-weight: bold;
    margin-bottom: 28rpx;
    .cny {
      font-size: 18rpx;
      color: #fff;
      margin-top: 4rpx;
      margin-left: 0;
      white-space: nowrap;
    }
  }
  .trade-btns {
    display: flex;
    padding: 2rpx;
    margin-bottom: 28rpx;
    gap: 0;
    background: #222;
    border-radius: 22rpx;
    .trade-type {
      flex: 1;
      height: 56rpx;
      font-size: 24rpx;
      font-weight: bold;
      border-radius: 18rpx;
      text-align: center;
      line-height: 56rpx;
      margin-left: 0;
      background: transparent;
      color: #fff;
      transition: all 0.2s;
      cursor: pointer;
      &.active {
        background: linear-gradient(90deg, #FFD700 0%, #00FF99 100%);
        color: #111;
      }
    }
  }
  .trade-info {
    color: #fff;
    font-size: 20rpx;
    margin-bottom: 4rpx;
  }
  .trade-amount {
    margin-bottom: 4rpx;
    color: #fff;
    font-size: 20rpx;
    .leverage-switch {
      display: flex;
      gap: 20rpx;
      margin-bottom: 18rpx;
    }
    .leverage-btn {
      min-width: 70rpx;
      height: 40rpx;
      line-height: 40rpx;
      text-align: center;
      border-radius: 8rpx;
      background: transparent;
      color: #fff;
      font-size: 22rpx;
      font-weight: bold;
      cursor: pointer;
      border: 1px solid #FFD70044;
      transition: all 0.2s;
    }
    .leverage-btn.active {
      background: #fff !important;
      color: #18191D !important;
      border: 1px solid #fff;
    }
    .amount-input {
      width: 100%;
      background: #1E1E1E !important;
      border-radius: 5rpx;
      border: none;
      color: #fff;
      font-size: 24rpx;
      padding: 0 20rpx;
      margin-top: 16rpx;
      height: 56rpx;
      box-sizing: border-box;
      &::placeholder {
        color: #999;
        opacity: 1;
      }
    }
  }
  .trade-confirm {
    background: #fff !important;
    color: #18191D !important;
    font-weight: bold;
    border: none;
    font-size: 26rpx;
    border-radius: 20rpx;
    margin-top: 18rpx;
    height: 56rpx;
    width: 100%;
    box-shadow: 0 2rpx 8rpx #FFD70033;
    transition: box-shadow 0.2s;
    &:active {
      box-shadow: 0 4rpx 16rpx #FFD70055;
    }
    &:disabled {
      background: #FFD70088;
      color: #999;
      box-shadow: none;
    }
  }
}
.futures-tabs {
  margin: 18rpx 24rpx 0 24rpx;
  background: #18191D !important;
  border-radius: 10rpx;
  border: 1px solid #FFD70033;
  padding: 0 0 10rpx 0;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  .tab-list {
    display: flex;
    border-bottom: 1px solid #FFD70022;
    .tab {
      flex: 1;
      text-align: center;
      font-size: 25rpx;
      color: #FFD70099;
      padding: 12rpx 0 8rpx 0;
      font-weight: 600;
      cursor: pointer;
      letter-spacing: 0.5px;
      &.active {
        color: #111;
        background: linear-gradient(90deg, #FFD700 0%, #FFEA70 100%);
        border-radius: 10rpx 10rpx 0 0;
        box-shadow: 0 1rpx 4rpx #FFD70033;
      }
    }
  }
  .tab-table {
    min-height: 700rpx;
    max-height: 700rpx;
    overflow-y: auto;
    .table-header, .table-row {
      display: flex;
      align-items: center;
      min-height: 40rpx;
      height: 48rpx;
    }
    .table-header text, .table-row text {
      flex: 1 1 0;
      min-width: 0;
      text-align: center;
      line-height: 48rpx;
      font-size: 24rpx;
    }
    .table-header {
      font-weight: bold;
      color: #fff;
      background: #181818;
      padding-top: 15rpx;
      padding-bottom: 15rpx;
    }
    .table-row {
      color: #fff;
      border-bottom: 1px solid #FFD70011;
      min-height: 32rpx;
      align-items: center;
      &:last-child {
        border-bottom: none;
      }
    }
    .table-pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 10rpx;
      button {
        background: #FFD700;
        color: #111;
        font-size: 24rpx;
        font-weight: bold;
        border-radius: 18rpx;
        padding: 8rpx 20rpx;
        margin: 0 10rpx;
        border: none;
        box-shadow: 0 2rpx 8rpx #FFD70033;
        transition: box-shadow 0.2s;
        &:active {
          box-shadow: 0 4rpx 16rpx #FFD70055;
        }
        &:disabled {
          background: #FFD70088;
          color: #999;
          box-shadow: none;
        }
      }
      text {
        font-size: 24rpx;
        color: #FFD700;
        margin: 0 10rpx;
      }
    }
  }
}
.footer-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60rpx;
  background: #181818;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid #FFD70033;
  z-index: 100;
  .footer-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 16rpx;
    color: #FFD70099;
    transition: color 0.2s, background 0.2s;
    &.active {
      color: #111;
      background: linear-gradient(90deg, #FFD700 0%, #FFEA70 100%);
      border-radius: 8rpx;
      padding: 4rpx 12rpx;
      box-shadow: 0 1rpx 4rpx #FFD70033;
    }
    text {
      margin-top: 2rpx;
    }
  }
}
.pair-popup {
  background: #18191D !important;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 0 0 0;
  min-height: 600rpx;
}
.pair-popup-title {
  color: #FFD700;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24rpx;
}
.pair-popup-list {
  max-height: 800rpx;
  overflow-y: auto;
}
.pair-popup-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #232323;
  cursor: pointer;
}
.pair-popup-item.active {
  background: #232323;
}
.pair-popup-logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}
.pair-popup-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.pair-popup-name {
  color: #FFD700;
  font-size: 28rpx;
  font-weight: bold;
}
.pair-popup-token {
  color: #bbb;
  font-size: 22rpx;
}
.pair-popup-price {
  display: flex;
  align-items: center;
  gap: 18rpx;
}
.pair-price {
  font-size: 26rpx;
  color: #fff;
}
.pair-change {
  padding: 6rpx 18rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  min-width: 120rpx;
  text-align: center;
  margin-left: 10rpx;
  display: inline-block;
}
.pair-change.rise {
  background: #02BF87 !important;
}
.pair-change.fall {
  background: #F34A69 !important;
}
.table-more {
  display: flex;
  justify-content: center;
  margin: 10rpx 0;
}
.table-more button {
  background: transparent;
  color: #fff !important;
  font-size: 22rpx;
  font-weight: bold;
  border-radius: 16rpx;
  padding: 0 32rpx;
  height: 40rpx;
  line-height: 40rpx;
  border: none;
  box-shadow: none;
  transition: none;
  min-width: 120rpx;
}
.table-more button:active {
  box-shadow: none;
}
.table-more button:disabled {
  background: transparent;
  color: #999;
  box-shadow: none;
}
.hold-card-list {
  display: flex;
  flex-direction: column;
  gap: 18rpx;
  padding: 18rpx 12rpx 18rpx 12rpx;
}
.hold-card {
  background: #18191D !important;
  border-radius: 14rpx;
  border: 1px solid #FFD70033;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  padding: 18rpx 20rpx;
}

.hold-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding-bottom: 8rpx;
  border-bottom: 1px solid #FFD70022;
}

.hold-symbol {
  color: #FFD700;
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 215, 0, 0.3);
}

.hold-direction {
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.close-position-btn {
  background: #FF4D4F !important;
  color: #fff !important;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  min-width: 120rpx;
  height: 48rpx;
  line-height: 32rpx;
  box-sizing: border-box;
}

.close-position-btn:disabled {
  background: #666 !important;
  color: #999 !important;
  margin: 0 6rpx;
}
.hold-row {
  display: flex;
  margin-bottom: 8rpx;
  .hold-label {
    color: #FFD700;
    width: 90rpx;
    font-size: 22rpx;
    font-weight: 600;
  }
  .hold-value {
    color: #fff;
    font-size: 22rpx;
    margin-left: 18rpx;
    flex: 1;
    word-break: break-all;
  }
}
.hold-row-2col {
  display: flex;
  flex-direction: row;
  margin-bottom: 8rpx;
}
.hold-col {
  flex: 1;
  display: flex;
  align-items: center;
  .hold-label {
    color: #FFD700;
    width: 70rpx;
    font-size: 22rpx;
    font-weight: 600;
  }
  .hold-value {
    color: #fff;
    font-size: 22rpx;
    margin-left: 12rpx;
    word-break: break-all;
  }
}

.hold-row-3col {
  display: flex;
  flex-direction: row;
  margin-bottom: 12rpx;
}

.hold-row-bottom {
  position: relative;
  margin-bottom: 0;
}

.hold-leverage {
  color: #FFD700;
  font-size: 20rpx;
  font-weight: bold;
  background: rgba(255, 215, 0, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.close-position-btn-bottom {
  position: absolute;
  right: 0;
  bottom: 0;
  background: #FF4D4F !important;
  color: #fff !important;
  border: none;
  border-radius: 8rpx;
  padding: 6rpx 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  height: 40rpx;
  line-height: 28rpx;
  box-sizing: border-box;
}

.close-position-btn-bottom:disabled {
  background: #666 !important;
  color: #999 !important;
}

.hold-col-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: 8rpx;
}

.hold-label-compact {
  color: #FFD700;
  font-size: 20rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  line-height: 1.2;
}

.hold-value-compact {
  color: #fff;
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
  word-break: break-all;
}

.hold-time-compact {
  white-space: nowrap;
  word-break: normal;
  font-size: 20rpx;
}

.close-position-btn-header {
  background: #FF4D4F !important;
  color: #fff !important;
  border: none;
  border-radius: 8rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  font-weight: bold;
  box-sizing: border-box;
  height: 36rpx;
  line-height: 24rpx;
}

.close-position-btn-header:disabled {
  background: #666 !important;
  color: #999 !important;
}
.red { color: #FF4D4F; }
.green { color: #00FF99; }
// 黑色背景上的所有文字
.futures-page,
.futures-header,
.futures-main,
.orderbook,
.trade-panel,
.futures-tabs,
.tab-table,
.hold-card-list,
.hold-card,
.table-header,
.table-row,
.hold-label,
.hold-value,
.pair-popup,
.pair-popup-title,
.pair-popup-list,
.pair-popup-item,
.pair-popup-name,
.pair-popup-token,
.pair-popup-price {
  color: #fff;
}
// 金色背景上的文字（按钮、激活tab、激活周期）
button,
.trade-confirm,
.tab-list .tab.active,
.period-list .period.active {
  color: #111 !important;
}
.futures-tabs .tab-list .tab.active {
  background: #fff !important;
  color: #18191D !important;
  font-weight: bold;
}
.futures-tabs .tab-list .tab {
  background: transparent;
  color: #fff;
}
.period-list .period {
  color: #fff;
}
/* 骨架屏样式 */
.skeleton-header {
  height: 60rpx;
  background: #18191D !important;
  border-radius: 10rpx;
  margin: 16rpx 16rpx 0 16rpx;
}
.skeleton-main {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  padding: 0 24rpx;
  margin-top: 12rpx;
}
.skeleton-orderbook {
  width: 50%;
  height: 260rpx;
  background: #18191D !important;
  border-radius: 10rpx;
}
.skeleton-trade-panel {
  width: 50%;
  background: #18191D !important;
  border-radius: 24rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.skeleton-timer {
  width: 60%;
  height: 40rpx;
  background: #222;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}
.skeleton-main-price {
  width: 80%;
  height: 40rpx;
  background: #222;
  border-radius: 10rpx;
  margin-bottom: 8rpx;
}
.skeleton-btns {
  width: 100%;
  height: 56rpx;
  background: #222;
  border-radius: 18rpx;
  margin-bottom: 8rpx;
}
.skeleton-input {
  width: 100%;
  height: 56rpx;
  background: #222;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
}
.skeleton-period-list {
  width: 100%;
  height: 40rpx;
  background: #222;
  border-radius: 18rpx;
  margin-bottom: 8rpx;
}
.skeleton-confirm {
  width: 100%;
  height: 56rpx;
  background: #222;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
}
.skeleton-tabs {
  margin: 18rpx 24rpx 0 24rpx;
}
.skeleton-tab {
  background: #18191D !important;
  border-radius: 10rpx;
  padding: 0 0 10rpx 0;
  margin-bottom: 18rpx;
}
.skeleton-tab-header {
  width: 100%;
  height: 48rpx;
  background: #222;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}
.skeleton-tab-row {
  width: 100%;
  height: 32rpx;
  background: #222;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}
.skeleton-row {
  height: 32rpx;
  background: #222;
  border-radius: 4rpx;
  margin-bottom: 15rpx;
  width: 100%;
  opacity: 0.5;
}
// 操作区主价格、顶部价格、涨跌幅颜色统一
.main-price {
  color: #02BF87 !important;
}
.cny {
  color: #02BF87 !important;
}
.trend-rate .rate {
  font-size: 26rpx !important;

  color: #02BF87 !important;
}
.trend-rate .rate.up {
  color: #02BF87 !important;
}
.trend-rate .rate.down {
  color: #F34A69 !important;
}
.leverage-switch-group .trade-type {
  background: transparent !important;
  color: #fff !important;
  border-radius: 0 !important;
  border: none !important;
  font-size: 24rpx;
  font-weight: bold;
  flex: 1;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  transition: all 0.2s;
}
.leverage-switch-group .trade-type.active {
  background: #fff !important;
  color: #18191D !important;
  border-radius: 18rpx !important;
}
// 输入框币种后缀样式
.amount-input.with-suffix {
  padding-right: 60rpx !important;
  height: 56rpx !important;
  line-height: 56rpx !important;
 
}
.input-suffix {
  position: absolute;
  right: 18rpx;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
  pointer-events: none;
  padding-top: 8rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  flex-direction: column;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  text-align: center;
}

.tab-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #fff;
  font-size: 24rpx;

  .loading-text {
    color: #FFD700;
    font-size: 26rpx;
  }
}
</style> 