import{s as a,d as e,e as t,h as l,w as s,i as o,o as r,l as i,q as d,F as n,j as c,m as u,t as f,x as h,v as m,y}from"./index-Qsizygi4.js";import{r as _}from"./request.BPygAub8.js";import{c as p}from"./index.B6QF5Ba_.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const w=g({data:()=>({leaderList:[],loading:!0,baseURL:p.apiBaseUrl,page:0,size:10,user:null,todayProfit:0,totalProfit:0}),computed:{isFollowing(){return this.user&&1===this.user.isFollowing},safeLeaderList(){return Array.isArray(this.leaderList)?this.leaderList.filter((a=>a&&"object"==typeof a)):[]},safeTotalProfit(){const a=(Number(this.totalProfit)||0).toFixed(4);return console.log("计算 safeTotalProfit:",this.totalProfit,"->",a),a},safeTodayProfit(){const a=(Number(this.todayProfit)||0).toFixed(4);return console.log("计算 safeTodayProfit:",this.todayProfit,"->",a),a}},onShow(){this.$nextTick((()=>{this.initPage()}))},onLoad(){this.initPage()},onUnload(){this.leaderList=[],this.user=null,this.loading=!1},methods:{async initPage(){try{this.loading=!0,await Promise.all([this.loadUserInfo(),this.loadLeaderList(),this.loadProfitSummary()]),await this.$nextTick()}catch(e){console.error("页面初始化失败:",e),a({title:"页面加载失败，请重试",icon:"none"})}finally{this.loading=!1,await this.$nextTick()}},async loadUserInfo(){try{const a=await _({url:"/api/user/info",method:"GET"});a&&a.data&&(this.user=a.data)}catch(a){this.user=null}},getImageUrl(a){if(!a)return"/static/default-avatar.png";try{return a.startsWith("/static/")?a:a.startsWith("/")?`${this.baseURL}${a}`:a}catch(e){return console.error("获取图片URL失败:",e),"/static/default-avatar.png"}},handleImageError(a){console.log("图片加载失败:",a),a.target&&(a.target.src="/static/default-avatar.png")},async loadLeaderList(){try{const a=await _({url:"/api/leader/summary",method:"GET"});a&&Array.isArray(a.data)?(this.leaderList=a.data,await this.$nextTick()):this.leaderList=[]}catch(a){throw console.error("加载带单员列表失败:",a),this.leaderList=[],a}},async loadProfitSummary(){try{console.log("开始加载收益汇总数据...");const a=await _({url:"/api/copy/profit/summary",method:"GET"});console.log("收益汇总接口响应:",a),a&&(0===a.code||200===a.code)&&a.data?(this.todayProfit=a.data.today_profit||0,this.totalProfit=a.data.total_profit||0,console.log("收益数据更新成功:",{todayProfit:this.todayProfit,totalProfit:this.totalProfit})):(console.warn("收益数据格式异常:",a),this.todayProfit=0,this.totalProfit=0)}catch(a){console.error("加载收益汇总失败:",a),this.todayProfit=0,this.totalProfit=0}},async refreshProfitData(){console.log("手动刷新收益数据..."),await this.loadProfitSummary(),a({title:"数据已刷新",icon:"success"})},handleFollow(t){const l=Number(t.min_follow_amount),s=Number(t.max_follow_amount),o=Number(this.user&&this.user.copyTradeBalance);isNaN(o)?a({title:"无法获取跟单账户余额",icon:"none"}):o<l?a({title:"跟单账户余额低于最低跟单金额",icon:"none"}):s>0&&o>s?a({title:"跟单账户余额高于最高跟单金额",icon:"none"}):e({title:"提示",content:"是否确认一键跟单？",confirmText:"确认",cancelText:"取消",success:e=>{e.confirm&&_({url:"/api/leader/follow",method:"POST",data:{leaderId:t.id}}).then((async()=>{a({title:"跟单成功",icon:"success"}),await this.loadUserInfo(),await this.loadLeaderList(),await this.$nextTick()})).catch((e=>{a({title:e.message||"操作失败",icon:"none"})}))}})},handleUnfollow(){e({title:"提示",content:"是否确认取消跟单？",confirmText:"确认",cancelText:"取消",success:e=>{e.confirm&&_({url:"/api/leader/unfollow",method:"POST"}).then((async()=>{a({title:"已取消跟单",icon:"success"}),await this.loadUserInfo(),await this.loadLeaderList(),await this.$nextTick()})).catch((e=>{a({title:e.message||"操作失败",icon:"none"})}))}})},goToMyCopy(){t({url:"/pages/copy/mycopy"})}}},[["render",function(a,e,t,_,p,g){const w=o,P=h,b=m,x=y;return r(),l(w,{class:"robot-follow-container"},{default:s((()=>[p.loading?(r(),l(w,{key:0},{default:s((()=>[(r(),i(n,null,d(3,(a=>c(w,{class:"skeleton-card",key:a,style:{"margin-bottom":"24rpx"}},{default:s((()=>[c(w,{class:"skeleton-avatar"}),c(w,{class:"skeleton-info"},{default:s((()=>[c(w,{class:"skeleton-title"}),c(w,{class:"skeleton-row"})])),_:1})])),_:2},1024))),64))])),_:1})):(r(),l(w,{key:1},{default:s((()=>[c(w,{class:"copy-balance-card"},{default:s((()=>[c(w,{class:"card-balance-row-flex"},{default:s((()=>[c(w,{class:"card-balance-left"},{default:s((()=>[c(w,{class:"card-header"},{default:s((()=>[u("跟单账户余额")])),_:1}),c(w,{class:"card-subtitle"},{default:s((()=>[u("跟单净利润")])),_:1}),c(w,{style:{display:"flex","align-items":"flex-end"}},{default:s((()=>[c(P,{class:"card-balance"},{default:s((()=>[u(f(g.safeTotalProfit),1)])),_:1}),c(P,{class:"card-unit"},{default:s((()=>[u("USDT")])),_:1})])),_:1}),c(w,{class:"card-today",style:{"margin-top":"18rpx"}},{default:s((()=>[u("今日收益 "),c(P,{class:"card-today-value"},{default:s((()=>[u(f(g.safeTodayProfit),1)])),_:1})])),_:1})])),_:1}),c(w,{class:"card-balance-right"},{default:s((()=>[c(b,{class:"card-arrow-img",src:"/assets/gendancenter-DpBJT3iI.png",mode:"widthFix",onClick:g.goToMyCopy},null,8,["onClick"]),c(P,{class:"card-center-text"},{default:s((()=>[u("我的跟单")])),_:1})])),_:1})])),_:1})])),_:1}),g.safeLeaderList.length>0?(r(),l(w,{key:0,class:"leader-list"},{default:s((()=>[(r(!0),i(n,null,d(g.safeLeaderList,((a,e)=>(r(),l(w,{class:"leader-card",key:`leader-${a.id||a.username||e}`},{default:s((()=>[c(w,{class:"leader-card-header"},{default:s((()=>[c(b,{src:g.getImageUrl(a.avatar),class:"leader-avatar",onError:g.handleImageError},null,8,["src","onError"]),c(w,{class:"leader-userinfo"},{default:s((()=>[c(w,{class:"leader-name"},{default:s((()=>[u(f(a.username||"未知用户"),1)])),_:2},1024),c(w,{class:"leader-level-inline"},{default:s((()=>[c(P,{class:"type-label"},{default:s((()=>[u(f(0===a.copy_type?"短线":1===a.copy_type?"中线":"长线"),1)])),_:2},1024),c(P,{class:"type-labels"},{default:s((()=>[u("交易周期 "+f(Number(a.lock_time||0)+"天"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(x,{class:"follow-btn",disabled:g.isFollowing&&p.user&&p.user.leaderId!==a.id||p.user&&1===p.user.isLeader,onClick:e=>p.user&&1===p.user.isFollowing&&p.user.leaderId===a.id?g.handleUnfollow():g.handleFollow(a)},{default:s((()=>[u(f(p.user&&1===p.user.isFollowing&&p.user.leaderId===a.id?"一键取消":"一键跟单"),1)])),_:2},1032,["disabled","onClick"])])),_:2},1024),c(w,{class:"leader-card-body-compact3"},{default:s((()=>[c(w,{class:"leader-row-compact3"},{default:s((()=>[c(w,null,{default:s((()=>[c(w,{class:"leader-label"},{default:s((()=>[u("储备金")])),_:1}),c(w,{class:"leader-value"},{default:s((()=>[u(f(Number(a.reserve_amount).toFixed(4)||"0"),1)])),_:2},1024)])),_:2},1024),c(w,null,{default:s((()=>[c(w,{class:"leader-label"},{default:s((()=>[u("最低跟单金额")])),_:1}),c(w,{class:"leader-value"},{default:s((()=>[u(f(a.min_follow_amount||"0"),1)])),_:2},1024)])),_:2},1024),c(w,null,{default:s((()=>[c(w,{class:"leader-label"},{default:s((()=>[u("最高跟单金额")])),_:1}),c(w,{class:"leader-value"},{default:s((()=>[u(f(0===Number(a.max_follow_amount||0)?"不限":a.max_follow_amount||"0"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(r(),l(w,{key:1,class:"empty-state"},{default:s((()=>[c(P,{class:"empty-text"},{default:s((()=>[u("暂无带单员数据")])),_:1})])),_:1}))])),_:1}))])),_:1})}],["__scopeId","data-v-5de989b6"]]);export{w as default};
