import{H as t,n as s,e as a,d as e,T as l,s as i,r as n,f as o,h as c,w as r,i as u,o as d,j as p,m as f,z as g,l as h,q as _,F as m,x as k,y as x,t as C}from"./index-Qsizygi4.js";import{_ as y}from"./uni-icons.CkwqPbHP.js";import{r as B}from"./uni-app.es.KXR2Mdtb.js";import{_ as j}from"./_plugin-vue_export-helper.BCo6x5W8.js";const H=j({data:()=>({statusBarHeight:0,settingsList:[{title:"修改登陆密码",path:"/pages/updatepassword/index"},{title:"设置安全密码",path:"/pages/updatesafepwd/index"}]}),created(){const s=t();this.statusBarHeight=s.statusBarHeight},methods:{handleBack(){s()},handleSettingClick(t){t.path&&a({url:t.path})},handleLogout(){e({title:"提示",content:"确定要退出登录吗？",success:t=>{t.confirm&&(l(),i({title:"已退出登录",icon:"success",duration:2e3}),setTimeout((()=>{n({url:"/pages/login/index"})}),2e3))}})}}},[["render",function(t,s,a,e,l,i){const n=B(o("uni-icons"),y),j=u,H=k,b=x;return d(),c(j,{class:"accountset-container"},{default:r((()=>[p(j,{class:"custom-navbar",style:g({paddingTop:l.statusBarHeight+"px"})},{default:r((()=>[p(j,{class:"navbar-content"},{default:r((()=>[p(j,{class:"left-area",onClick:i.handleBack},{default:r((()=>[p(n,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),p(H,{class:"page-title"},{default:r((()=>[f("账户设置")])),_:1}),p(j,{class:"right-area"})])),_:1})])),_:1},8,["style"]),p(j,{class:"content-box"},{default:r((()=>[p(j,{class:"settings-list"},{default:r((()=>[(d(!0),h(m,null,_(l.settingsList,((t,s)=>(d(),c(j,{class:"setting-item",key:s,onClick:s=>i.handleSettingClick(t)},{default:r((()=>[p(H,{class:"item-text"},{default:r((()=>[f(C(t.title),1)])),_:2},1024),p(n,{type:"right",size:"16",color:"#fff"})])),_:2},1032,["onClick"])))),128))])),_:1}),p(b,{class:"logout-btn",onClick:i.handleLogout},{default:r((()=>[f("退出登录")])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-54c27300"]]);export{H as default};
