import{H as e,s as t,L as s,T as a,r as n,M as o,n as l,e as i,f as r,h as c,w as d,i as u,o as h,j as f,m,z as p,t as g,u as y,x as _,I as C,y as v,V as b,W as k,X as x}from"./index-Qsizygi4.js";import{_ as I}from"./uni-icons.CkwqPbHP.js";import{r as T}from"./uni-app.es.KXR2Mdtb.js";import{r as A}from"./request.BPygAub8.js";import{c as w}from"./index.B6QF5Ba_.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";const V=B({data:()=>({statusBarHeight:0,phone:"",verifyCode:"",counting:0,timer:null,isAgree:!1,userInfo:{}}),computed:{verifyBtnText(){return this.counting>0?`重新发送(${this.counting}s)`:"发送验证码"}},created(){const t=e();this.statusBarHeight=t.statusBarHeight},beforeDestroy(){this.timer&&(clearInterval(this.timer),this.timer=null)},onShow(){this.getUserInfo()},methods:{goToAgreement(e){const t=`${w.baseUrl}/#/pages/agreement/index?id=${e}&from=A`;window.location.href=t},async getUserInfo(){try{const e=await A({url:"/api/user/info",method:"GET"});200===e.code&&(this.userInfo=e.data)}catch(e){console.error("获取用户信息失败",e)}},async sendVerifyCode(){if(!(this.counting>0))if(this.phone&&/^1[3-9]\d{9}$/.test(this.phone))if(this.isAgree)try{200===(await A({url:"/api/user/phone/sendCode",method:"POST",params:{newPhone:this.phone}})).code&&(t({title:"验证码已发送",icon:"success"}),this.counting=60,this.timer=setInterval((()=>{this.counting>0?this.counting--:(clearInterval(this.timer),this.timer=null)}),1e3))}catch(e){t({title:e.message||"发送失败",icon:"none"})}else t({title:"请阅读并同意相关协议",icon:"none"});else t({title:"请输入正确的手机号",icon:"none"})},async handleSubmit(){if(this.userInfo.phone!==this.phone)if("13800138002"!==this.userInfo.phone)if(this.phone&&/^1[3-9]\d{9}$/.test(this.phone))if(this.verifyCode)if(this.isAgree)try{s({title:"提交中..."});200===(await A({url:"/api/user/phone/update",method:"POST",data:{newPhone:this.phone,verifyCode:this.verifyCode}})).code&&(t({title:"手机号修改成功",icon:"success"}),setTimeout((()=>{a(),n({url:"/pages/login/index"})}),1500))}catch(e){t({title:e.message||"修改失败",icon:"none"})}finally{o()}else t({title:"请阅读并同意相关协议",icon:"none"});else t({title:"请输入验证码",icon:"none"});else t({title:"请输入正确的手机号",icon:"none"});else t({title:"当前手机号码不能修改",icon:"none"});else t({title:"当前手机号码与当前输入手机号码相同",icon:"none"})},handleBack(){l()},handleAgreementChange(e){this.isAgree=e.detail.value.length>0},goToPrivacy(){i({url:"/pages/legalsystem/index"})}}},[["render",function(e,t,s,a,n,o){const l=T(r("uni-icons"),I),i=u,A=_,w=C,B=v,V=b,j=k,H=x;return h(),c(i,{class:"phone-container"},{default:d((()=>[f(i,{class:"custom-navbar",style:p({paddingTop:n.statusBarHeight+"px"})},{default:d((()=>[f(i,{class:"navbar-content"},{default:d((()=>[f(i,{class:"left-area",onClick:o.handleBack},{default:d((()=>[f(l,{type:"left",size:"20",color:"#22d1ee"})])),_:1},8,["onClick"]),f(A,{class:"page-title"},{default:d((()=>[m("修改手机号")])),_:1}),f(i,{class:"right-area"})])),_:1})])),_:1},8,["style"]),f(i,{class:"form-content"},{default:d((()=>[f(i,{class:"phone-section"},{default:d((()=>[f(A,{class:"label"},{default:d((()=>[m("当前绑定手机：")])),_:1}),f(A,{class:"phone"},{default:d((()=>[m(g(n.userInfo.phone),1)])),_:1})])),_:1}),f(i,{class:"form-section"},{default:d((()=>[f(i,{class:"form-item"},{default:d((()=>[f(A,{class:"form-label"},{default:d((()=>[m("手机号")])),_:1}),f(w,{class:"form-input",type:"number",maxlength:"11",modelValue:n.phone,"onUpdate:modelValue":t[0]||(t[0]=e=>n.phone=e),placeholder:"请输入手机号","placeholder-style":"color: rgba(255, 255, 255, 0.3);"},null,8,["modelValue"])])),_:1}),f(i,{class:"form-item"},{default:d((()=>[f(A,{class:"form-label"},{default:d((()=>[m("验证码")])),_:1}),f(i,{class:"verify-group"},{default:d((()=>[f(w,{class:"form-input",type:"text",modelValue:n.verifyCode,"onUpdate:modelValue":t[1]||(t[1]=e=>n.verifyCode=e),placeholder:"验证码","placeholder-style":"color: rgba(255, 255, 255, 0.3);"},null,8,["modelValue"]),f(B,{class:"verify-btn",disabled:n.counting>0,onClick:o.sendVerifyCode},{default:d((()=>[m(g(o.verifyBtnText),1)])),_:1},8,["disabled","onClick"])])),_:1})])),_:1})])),_:1}),f(i,{class:"agreement-section"},{default:d((()=>[f(H,{onChange:o.handleAgreementChange},{default:d((()=>[f(j,{class:"agreement-label"},{default:d((()=>[f(V,{checked:n.isAgree,color:"#22d1ee",style:{transform:"scale(0.7)"}},null,8,["checked"]),f(A,{class:"agreement-text"},{default:d((()=>[m(" 我已阅读并同意 "),f(A,{class:"link",onClick:t[2]||(t[2]=y((e=>o.goToAgreement(1)),["stop"]))},{default:d((()=>[m("《用户协议》")])),_:1}),m(" 及 "),f(A,{class:"link",onClick:t[3]||(t[3]=y((e=>o.goToAgreement(2)),["stop"]))},{default:d((()=>[m("《隐私政策》")])),_:1})])),_:1})])),_:1})])),_:1},8,["onChange"])])),_:1}),f(B,{class:"submit-btn",onClick:o.handleSubmit},{default:d((()=>[m("确定修改")])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-849356d0"]]);export{V as default};
